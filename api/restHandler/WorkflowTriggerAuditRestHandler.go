/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package restHandler

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/bean"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/integration"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/repository"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/service"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
	"strconv"
)

type WorkflowTriggerAuditRestHandler interface {
	// GetTriggerAuditHistory gets trigger audit history for a pipeline
	GetTriggerAuditHistory(w http.ResponseWriter, r *http.Request)
	
	// GetTriggerAuditById gets trigger audit by ID
	GetTriggerAuditById(w http.ResponseWriter, r *http.Request)
	
	// GetWorkflowConfigSnapshot gets workflow config snapshot by audit ID
	GetWorkflowConfigSnapshot(w http.ResponseWriter, r *http.Request)
	
	// RetriggerFromAudit retriggers workflow from audit snapshot
	RetriggerFromAudit(w http.ResponseWriter, r *http.Request)
	
	// GetTriggerAuditSummary gets trigger audit summary for dashboard
	GetTriggerAuditSummary(w http.ResponseWriter, r *http.Request)
}

type WorkflowTriggerAuditRestHandlerImpl struct {
	logger                      *zap.SugaredLogger
	userService                 user.UserService
	validator                   *validator.Validate
	enforcer                    casbin.Enforcer
	workflowTriggerAuditService service.WorkflowTriggerAuditService
	retriggerIntegration        integration.RetriggerIntegration
}

func NewWorkflowTriggerAuditRestHandlerImpl(
	logger *zap.SugaredLogger,
	userService user.UserService,
	validator *validator.Validate,
	enforcer casbin.Enforcer,
	workflowTriggerAuditService service.WorkflowTriggerAuditService,
	retriggerIntegration integration.RetriggerIntegration) *WorkflowTriggerAuditRestHandlerImpl {
	
	return &WorkflowTriggerAuditRestHandlerImpl{
		logger:                      logger,
		userService:                 userService,
		validator:                   validator,
		enforcer:                    enforcer,
		workflowTriggerAuditService: workflowTriggerAuditService,
		retriggerIntegration:        retriggerIntegration,
	}
}

func (handler *WorkflowTriggerAuditRestHandlerImpl) GetTriggerAuditHistory(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		handler.logger.Errorw("request err, GetTriggerAuditHistory", "err", err, "pipelineId", vars["pipelineId"])
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	
	workflowType := r.URL.Query().Get("workflowType")
	if workflowType == "" {
		workflowType = "CI" // default to CI
	}
	
	limit := 20 // default limit
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil {
			limit = parsedLimit
		}
	}
	
	offset := 0 // default offset
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil {
			offset = parsedOffset
		}
	}
	
	// RBAC check - user should have view access to the pipeline
	// This would need to be implemented based on your RBAC system
	// For now, we'll assume the user has access
	
	workflowTypeEnum := repository.WorkflowType(workflowType)
	audits, err := handler.workflowTriggerAuditService.GetTriggerAuditHistory(pipelineId, workflowTypeEnum, limit, offset)
	if err != nil {
		handler.logger.Errorw("service err, GetTriggerAuditHistory", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	
	response := &bean.TriggerAuditHistoryResponse{
		TriggerAudits: audits,
		TotalCount:    len(audits), // This should be actual total count from service
	}
	
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *WorkflowTriggerAuditRestHandlerImpl) GetTriggerAuditById(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	
	vars := mux.Vars(r)
	auditId, err := strconv.Atoi(vars["auditId"])
	if err != nil {
		handler.logger.Errorw("request err, GetTriggerAuditById", "err", err, "auditId", vars["auditId"])
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	
	workflowId, err := strconv.Atoi(vars["workflowId"])
	if err != nil {
		handler.logger.Errorw("request err, GetTriggerAuditById", "err", err, "workflowId", vars["workflowId"])
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	
	workflowType := r.URL.Query().Get("workflowType")
	if workflowType == "" {
		workflowType = "CI" // default to CI
	}
	
	// RBAC check - user should have view access to the workflow
	// This would need to be implemented based on your RBAC system
	
	workflowTypeEnum := repository.WorkflowType(workflowType)
	audit, err := handler.workflowTriggerAuditService.GetTriggerAuditByWorkflowId(workflowId, workflowTypeEnum)
	if err != nil {
		handler.logger.Errorw("service err, GetTriggerAuditById", "err", err, "auditId", auditId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	
	common.WriteJsonResp(w, nil, audit, http.StatusOK)
}

func (handler *WorkflowTriggerAuditRestHandlerImpl) GetWorkflowConfigSnapshot(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	
	vars := mux.Vars(r)
	auditId, err := strconv.Atoi(vars["auditId"])
	if err != nil {
		handler.logger.Errorw("request err, GetWorkflowConfigSnapshot", "err", err, "auditId", vars["auditId"])
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	
	// RBAC check - user should have view access to the workflow
	// This would need to be implemented based on your RBAC system
	
	config, err := handler.workflowTriggerAuditService.GetWorkflowConfigForRetrigger(auditId)
	if err != nil {
		handler.logger.Errorw("service err, GetWorkflowConfigSnapshot", "err", err, "auditId", auditId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	
	common.WriteJsonResp(w, nil, config, http.StatusOK)
}

func (handler *WorkflowTriggerAuditRestHandlerImpl) RetriggerFromAudit(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	
	vars := mux.Vars(r)
	auditId, err := strconv.Atoi(vars["auditId"])
	if err != nil {
		handler.logger.Errorw("request err, RetriggerFromAudit", "err", err, "auditId", vars["auditId"])
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	
	// RBAC check - user should have trigger access to the pipeline
	// This would need to be implemented based on your RBAC system
	
	workflowId, err := handler.retriggerIntegration.RetriggerFromAudit(auditId, int32(userId))
	if err != nil {
		handler.logger.Errorw("service err, RetriggerFromAudit", "err", err, "auditId", auditId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	
	response := &bean.RetriggerResponse{
		WorkflowId: workflowId,
		Status:     "SUCCESS",
		Message:    "Workflow retriggered successfully",
	}
	
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *WorkflowTriggerAuditRestHandlerImpl) GetTriggerAuditSummary(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	
	// Parse query parameters for filters
	var filter bean.TriggerAuditFilter
	if err := json.NewDecoder(r.Body).Decode(&filter); err != nil {
		// If no body, use query parameters
		filter = bean.TriggerAuditFilter{
			Limit:  20,
			Offset: 0,
		}
		
		if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil {
				filter.Limit = parsedLimit
			}
		}
		
		if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
			if parsedOffset, err := strconv.Atoi(offsetStr); err == nil {
				filter.Offset = parsedOffset
			}
		}
	}
	
	// RBAC check - user should have view access to the apps/pipelines
	// This would need to be implemented based on your RBAC system
	
	// This would need to be implemented in the service
	// For now, return a placeholder response
	summary := []bean.TriggerAuditSummary{
		{
			PipelineId:         1,
			PipelineName:       "sample-pipeline",
			AppId:              1,
			AppName:            "sample-app",
			TotalTriggers:      10,
			SuccessfulTriggers: 8,
			FailedTriggers:     2,
		},
	}
	
	common.WriteJsonResp(w, nil, summary, http.StatusOK)
}
