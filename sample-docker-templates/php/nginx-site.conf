server {
	listen   80; ## listen for ipv4; this line is default and implied
	listen   [::]:80 default ipv6only=on; ## listen for ipv6

	root /var/www/html;
	index index.php index.html index.htm;

	# Make site accessible from http://localhost/
	server_name _;

	# Disable sendfile as per https://docs.vagrantup.com/v2/synced-folders/virtualbox.html
	sendfile off;

	# Add stdout logging

	error_log /dev/stdout info;
	access_log /dev/stdout;

	location / {
		try_files $uri $uri/ =404;
	}

        error_page 404 /404.html;
        location = /404.html {
                root /var/www/errors;
                internal;
        }

        location ^~ /ngd-style.css {
            alias /var/www/errors/style.css;
            access_log off;
        }

        location ^~ /ngd-sad.svg {
            alias /var/www/errors/sad.svg;
            access_log off;
        }

	# Pass the PHP scripts to FastCGI server listening on socket
	location ~ \.php$ {
                try_files $uri =404;
		fastcgi_split_path_info ^(.+\.php)(/.+)$;
		fastcgi_pass unix:/run/php/php7.0-fpm.sock;
		fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    		fastcgi_param SCRIPT_NAME $fastcgi_script_name;
		fastcgi_index index.php;
		include fastcgi_params;
	}

        location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml)$ {
                expires           5d;
        }

	# deny access to . files, for security
	#
	location ~ /\. {
    		log_not_found off;
    		deny all;
	}

        location ^~ /.well-known {
                allow all;
                auth_basic off;
        }

}
