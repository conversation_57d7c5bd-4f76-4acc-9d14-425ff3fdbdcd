# Google

## Sample Configuration

![](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/google.jpg)

---

## Values You Would Require at SSO Provider

Devtron provides a sample configuration out of the box. There are some values that you need to either get from your SSO provider or give to your SSO provider.

### Values to Fetch

* clientID

* clientSecret

    ![Fetching Client ID and Secret](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/secret/google-id-secret.jpg)


### Values to Provide

* redirectURI (provided in SSO Login Services by Devtron)

    ![Copying Redirect URI from Devtron](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/redirect/google-redurl.jpg)

    ![Pasting Redirect URI](https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/global-configurations/sso-login-service/redirect/google-redirect.jpg)

---

## Reference

* [View Google Documentation](https://developers.google.com/identity/gsi/web/guides/get-google-api-clientid)

* [View Dex IdP Documentation](https://dexidp.io/docs/connectors/google/)



