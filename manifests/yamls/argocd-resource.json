[{"op": "add", "path": "/data/resource.customizations", "value": "kubernetes-client.io/ExternalSecret:\n  health.lua: |\n    hs = {}\n    if obj.status ~= nil then\n      if obj.status.status ~= nil then\n          hs.status = \"Degraded\"\n          hs.message = obj.status.status\n      else\n        hs.status = \"Healthy\"\n      end\n    else\n      hs.status = \"Healthy\"\n    end\n    return hs\nargoproj.io/Rollout:\n  health.lua: |\n    function checkReplicasStatus(obj)\n      hs = {}\n      replicasCount = getNumberValueOrDefault(obj.spec.replicas)\n      replicasStatus = getNumberValueOrDefault(obj.status.replicas)\n      updatedReplicas = getNumberValueOrDefault(obj.status.updatedReplicas)\n      availableReplicas = getNumberValueOrDefault(obj.status.availableReplicas)\n\n      if updatedReplicas < replicasCount then\n        hs.status = \"Progressing\"\n        hs.message = \"Waiting for roll out to finish: More replicas need to be updated\"\n        return hs\n      end\n      -- Since the scale down delay can be very high, BlueGreen does not wait for all the old replicas to scale\n      -- down before marking itself healthy. As a result, only evaluate this condition if the strategy is canary.\n      if obj.spec.strategy.canary ~= nil and replicasStatus > updatedReplicas then\n        hs.status = \"Progressing\"\n        hs.message = \"Waiting for roll out to finish: old replicas are pending termination\"\n        return hs\n      end\n      if availableReplicas < updatedReplicas then\n        hs.status = \"Progressing\"\n        hs.message = \"Waiting for roll out to finish: updated replicas are still becoming available\"\n        return hs\n      end\n      return nil\n    end\n\n    function getNumberValueOrDefault(field)\n      if field ~= nil then\n        return field\n      end\n      return 0\n    end\n\n    function checkPaused(obj)\n      hs = {}\n      local paused = false\n      if obj.status.verifyingPreview ~= nil then\n        paused = obj.status.verifyingPreview\n      elseif obj.spec.paused ~= nil then\n        paused = obj.spec.paused\n      end\n\n      if paused then\n        hs.status = \"Suspended\"\n        hs.message = \"Rollout is paused\"\n        return hs\n      end\n      return nil\n    end\n\n    hs = {}\n    if obj.status ~= nil then\n      if obj.status.conditions ~= nil then\n        for _, condition in ipairs(obj.status.conditions) do\n          if condition.type == \"InvalidSpec\" then\n            hs.status = \"Degraded\"\n            hs.message = condition.message\n            return hs\n          end\n          if condition.type == \"Progressing\" and condition.reason == \"RolloutAborted\" then\n            hs.status = \"Degraded\"\n            hs.message = condition.message\n            return hs\n          end\n          if condition.type == \"Progressing\" and condition.reason == \"ProgressDeadlineExceeded\" then\n            hs.status = \"Degraded\"\n            hs.message = condition.message\n            return hs\n          end\n        end\n      end\n      if obj.status.currentPodHash ~= nil then\n        if obj.spec.strategy.blueGreen ~= nil then\n          isPaused = checkPaused(obj)\n          if isPaused ~= nil then\n            return isPaused\n          end\n          replicasHS = checkReplicasStatus(obj)\n          if replicasHS ~= nil then\n            return replicasHS\n          end\n          if obj.status.blueGreen ~= nil and obj.status.blueGreen.activeSelector ~= nil and obj.status.blueGreen.activeSelector == obj.status.currentPodHash then\n            hs.status = \"Healthy\"\n            hs.message = \"The active Service is serving traffic to the current pod spec\"\n            return hs\n          end\n          hs.status = \"Progressing\"\n          hs.message = \"The current pod spec is not receiving traffic from the active service\"\n          return hs\n        end\n        if obj.spec.strategy.recreate ~= nil then\n          isPaused = checkPaused(obj)\n          if isPaused ~= nil then\n            return isPaused\n          end\n          replicasHS = checkReplicasStatus(obj)\n          if replicasHS ~= nil then\n            return replicasHS\n          end\n          if obj.status.recreate ~= nil and obj.status.recreate.currentRS ~= nil and obj.status.recreate.currentRS == obj.status.currentPodHash then\n            hs.status = \"Healthy\"\n            hs.message = \"Rollout is successful\"\n            return hs\n          end\n          hs.status = \"Progressing\"\n          hs.message = \"Rollout is in progress\"\n          return hs\n        end\n        if obj.spec.strategy.canary ~= nil then\n          currentRSIsStable = obj.status.canary.stableRS == obj.status.currentPodHash\n          if obj.spec.strategy.canary.steps ~= nil and table.getn(obj.spec.strategy.canary.steps) > 0 then\n            stepCount = table.getn(obj.spec.strategy.canary.steps)\n            if obj.status.currentStepIndex ~= nil then\n              currentStepIndex = obj.status.currentStepIndex\n              isPaused = checkPaused(obj)\n              if isPaused ~= nil then\n                return isPaused\n              end\n          \n              if paused then\n                hs.status = \"Suspended\"\n                hs.message = \"Rollout is paused\"\n                return hs\n              end\n              if currentRSIsStable and stepCount == currentStepIndex then\n                replicasHS = checkReplicasStatus(obj)\n                if replicasHS ~= nil then\n                  return replicasHS\n                end\n                hs.status = \"Healthy\"\n                hs.message = \"The rollout has completed all steps\"\n                return hs\n              end\n            end\n            hs.status = \"Progressing\"\n            hs.message = \"Waiting for rollout to finish steps\"\n            return hs\n          end\n\n          -- The detecting the health of the Canary deployment when there are no steps\n          replicasHS = checkReplicasStatus(obj)\n          if replicasHS ~= nil then\n            return replicasHS\n          end\n          if currentRSIsStable then\n            hs.status = \"Healthy\"\n            hs.message = \"The rollout has completed canary deployment\"\n            return hs\n          end\n          hs.status = \"Progressing\"\n          hs.message = \"Waiting for rollout to finish canary deployment\"\n        end\n      end\n    end\n    hs.status = \"Progressing\"\n    hs.message = \"Waiting for rollout to finish: status has not been reconciled.\"\n    return hs\n"}]