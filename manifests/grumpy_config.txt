[req]
req_extensions = v3_req
distinguished_name = req_distinguished_name
[ req_distinguished_name ]
[ v3_req ]
basicConstraints=CA:FALSE
subjectAltName=@alt_names
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth

[ alt_names ]
DNS.1 = guard-service
DNS.2 = guard-service.devtroncd
DNS.3 = guard-service.devtroncd.svc
DNS.4 = guard-service.devtroncd.svc.cluster.local