openapi: 3.0.3
info:
  title: Devtron Labs
  version: 1.0.0
servers:
- url: /
paths:
  /orchestrator/application/rollback:
    put:
      description: Rollback application if the application is installed from the chartStore
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RollbackReleaseRequest'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RollbackReleaseResponse'
          description: application rollback response
  /orchestrator/application/template-chart:
    post:
      description: Helm template chart
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TemplateChartRequest'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateChartResponse'
          description: template chart response
components:
  schemas:
    RollbackReleaseResponse:
      example:
        success: true
      properties:
        success:
          description: success or failure
          example: true
          type: boolean
      type: object
    RollbackReleaseRequest:
      example:
        installedAppVersionId: 2
        installedAppId: 1
        hAppId: 1|default|someName
        version: 10
      properties:
        installedAppId:
          description: Installed App Id if the app is installed from chart store
          example: 1
          type: integer
        installedAppVersionId:
          description: Installed App Version Id if the app is installed from chart
            store
          example: 2
          type: integer
        hAppId:
          description: helm App Id if the application is installed from using helm
            (for example "clusterId|namespace|appName" )
          example: 1|default|someName
          type: string
        version:
          description: rollback to this version
          example: 10
          type: integer
      type: object
    TemplateChartRequest:
      example:
        appStoreApplicationVersionId: 10
        valuesYaml: some values yaml
        environmentId: 1
        releaseName: some name
        namespace: "1"
        clusterId: 1
      properties:
        environmentId:
          description: environment Id on which helm template would be performed
          example: 1
          type: integer
        clusterId:
          description: If environmentId is not provided, clusterId should be passed
          example: 1
          type: integer
        namespace:
          description: If environmentId is not provided, namespace should be passed
          example: "1"
          type: string
        releaseName:
          description: release name of helm app (if not provided, some random name
            is picked)
          example: some name
          type: string
        appStoreApplicationVersionId:
          description: App store application version Id
          example: 10
          type: integer
        valuesYaml:
          description: Values yaml
          example: some values yaml
          type: string
      type: object
    TemplateChartResponse:
      example:
        manifest: some manifest
      properties:
        manifest:
          description: helm generated manifest
          example: some manifest
          type: string
      type: object
