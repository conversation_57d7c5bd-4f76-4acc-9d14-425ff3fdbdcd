openapi: 3.0.0
info:
  title: OPI Orchestrator API
  description: API for fetching user resource options based on kind and version.
  version: 1.0.0

paths:
  /orchestrator/user/resource/options/{kind}/{version}:
    post:
      summary: Get user resource options
      description: Returns options based on the kind and version of the user resource.
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - team
              - environment
              - application/devtron-application
              - application/helm-application
              - environment/helm
              - cluster
              - chartGroup
              - jobs
              - workflow
              - cluster/namespaces
              - cluster/apiResources
              - cluster/resources
          description: Type of user resource.
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - alpha1
          description: API version.

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - entity
              properties:
                entity:
                  type: string
                accessType:
                  type: string
                teamIds:
                  type: array
                  items:
                    type: integer
                appNames:
                  type: array
                  items:
                    type: string
                clusterId:
                  type: integer
                k8sRequest:
                  type: object
                  properties:
                    resourceIdentifier:
                      type: object
                      properties:
                        groupVersionKind:
                          type: object
                          properties:
                            Group:
                              type: string
                              example: "generators.external-secrets.io"
                            Version:
                              type: string
                              example: "v1alpha1"
                            Kind:
                              type: string
                              example: "ACRAccessToken"
                        namespace:
                          type: string
                          example: ""



      responses:
        '200':
          description: Returns Resource Options as response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                  result:
                    $ref: '#/components/schemas/ResourceOptions'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  schemas:
    ResourceOptions:
      required:
        - data
      properties:
        data:
          type: object
          description: generic data field which will contain relevant data
    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      type: object
      required:
        - code
        - status
      properties:
        code:
          type: string
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message