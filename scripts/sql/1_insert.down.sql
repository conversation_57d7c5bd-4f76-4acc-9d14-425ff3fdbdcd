DROP TABLE "public"."cd_workflow_runner" CASCADE;

DROP TABLE "public"."chart_ref" CASCADE;

DROP TABLE "public"."role_group_role_mapping" CASCADE;

DROP TABLE "public"."helm_values" CASCADE;

DROP TABLE "public"."slack_config" CASCADE;

DROP TABLE "public"."image_scan_deploy_info" CASCADE;

DROP TABLE "public"."app" CASCADE;

DROP TABLE "public"."notification_settings_view" CASCADE;

DROP TABLE "public"."ci_pipeline" CASCADE;

DROP TABLE "public"."config_map_app_level" CASCADE;

DROP TABLE "public"."external_ci_pipeline" CASCADE;

DROP TABLE "public"."app_store_version_values" CASCADE;

DROP TABLE "public"."pipeline_strategy" CASCADE;

DROP TABLE "public"."chart_env_config_override" CASCADE;

DROP TABLE "public"."config_map_env_level" CASCADE;

DROP TABLE "public"."deployment_status" CASCADE;

DROP TABLE "public"."pipeline_config_override" CASCADE;

DROP TABLE "public"."cluster" CASCADE;

DROP TABLE "public"."cve_store" CASCADE;

DROP TABLE "public"."image_scan_execution_history" CASCADE;

DROP TABLE "public"."image_scan_execution_result" CASCADE;

DROP TABLE "public"."image_scan_object_meta" CASCADE;

DROP TABLE "public"."environment" CASCADE;

DROP TABLE "public"."app_store_application_version" CASCADE;

DROP TABLE "public"."ci_pipeline_material" CASCADE;

DROP TABLE "public"."roles" CASCADE;

DROP TABLE "public"."env_level_app_metrics" CASCADE;

DROP TABLE "public"."team" CASCADE;

DROP TABLE "public"."config_map_pipeline_level" CASCADE;

DROP TABLE "public"."app_level_metrics" CASCADE;

DROP TABLE "public"."db_migration_config" CASCADE;

DROP TABLE "public"."cd_workflow_config" CASCADE;

DROP TABLE "public"."charts" CASCADE;

DROP TABLE "public"."pipeline" CASCADE;

DROP TABLE "public"."deployment_group_app" CASCADE;

DROP TABLE "public"."cluster_helm_config" CASCADE;

DROP TABLE "public"."app_store" CASCADE;

DROP TABLE "public"."cve_policy_control" CASCADE;

DROP TABLE "public"."chart_repo" CASCADE;

DROP TABLE "public"."docker_artifact_store" CASCADE;

DROP TABLE "public"."chart_group_entry" CASCADE;

DROP TABLE "public"."notification_templates" CASCADE;

DROP TABLE "public"."chart_group" CASCADE;

DROP TABLE "public"."users" CASCADE;

DROP TABLE "public"."chart_group_deployment" CASCADE;

DROP TABLE "public"."ci_workflow_config" CASCADE;

DROP TABLE "public"."installed_apps" CASCADE;

DROP TABLE "public"."app_workflow_mapping" CASCADE;

DROP TABLE "public"."event" CASCADE;

DROP TABLE "public"."ci_template" CASCADE;

DROP TABLE "public"."notifier_event_log" CASCADE;

DROP TABLE "public"."ci_workflow" CASCADE;

DROP TABLE "public"."role_group" CASCADE;

DROP TABLE "public"."cluster_installed_apps" CASCADE;

DROP TABLE "public"."deployment_group" CASCADE;

DROP TABLE "public"."installed_app_versions" CASCADE;

DROP TABLE "public"."notification_settings" CASCADE;

DROP TABLE "public"."ses_config" CASCADE;

DROP TABLE "public"."ci_pipeline_scripts" CASCADE;

DROP TABLE "public"."user_roles" CASCADE;

DROP TABLE "public"."app_workflow" CASCADE;

DROP TABLE "public"."cd_workflow" CASCADE;

DROP TABLE "public"."db_config" CASCADE;

DROP TABLE "public"."cluster_accounts" CASCADE;

DROP TABLE "public"."git_web_hook" CASCADE;

DROP TABLE "public"."events" CASCADE;

DROP TABLE "public"."project_management_tool_config" CASCADE;

DROP TABLE "public"."app_env_linkouts" CASCADE;

DROP TABLE "public"."git_material" CASCADE;

DROP TABLE "public"."job_event" CASCADE;

DROP TABLE "public"."ci_artifact" CASCADE;

DROP TABLE "public"."git_provider" CASCADE;