BEGIN;

-- Create Sequence for workflow_config_snapshot
CREATE SEQUENCE IF NOT EXISTS id_seq_workflow_config_snapshot;

-- Table Definition: workflow_config_snapshot (single simplified table)
CREATE TABLE IF NOT EXISTS "public"."workflow_config_snapshot" (
    "id"                    int             NOT NULL DEFAULT nextval('id_seq_workflow_config_snapshot'::regclass),
    "workflow_id"           int             NOT NULL, -- ci_workflow.id or cd_workflow_runner.id
    "workflow_type"         varchar(20)     NOT NULL, -- CI, PRE_CD, POST_CD
    "pipeline_id"           int             NOT NULL,
    "app_id"                int             NOT NULL,
    "environment_id"        int,            -- null for CI
    "artifact_id"           int,            -- ci_artifact.id
    "trigger_type"          varchar(20)     NOT NULL, -- MANUAL, AUTO, WEBHOOK
    "triggered_by"          int4            NOT NULL,
    "trigger_metadata"      jsonb,          -- additional trigger context
    "infra_config_snapshot_id"    int,      -- reference to existing infra_config_trigger_history
    "workflow_request_json" text            NOT NULL, -- complete WorkflowRequest JSON (contains everything)
    "git_materials_json"    text,           -- git materials snapshot (like ci_pipeline_history)
    "schema_version"        int             NOT NULL DEFAULT 1, -- for backward compatibility
    "created_on"            timestamptz     NOT NULL,
    "created_by"            int4            NOT NULL,
    "updated_on"            timestamptz     NOT NULL,
    "updated_by"            int4            NOT NULL,
    PRIMARY KEY ("id"),
    UNIQUE ("workflow_id", "workflow_type")
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_pipeline_id ON "public"."workflow_config_snapshot"("pipeline_id");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_app_id ON "public"."workflow_config_snapshot"("app_id");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_workflow_type ON "public"."workflow_config_snapshot"("workflow_type");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_created_on ON "public"."workflow_config_snapshot"("created_on");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_workflow_id ON "public"."workflow_config_snapshot"("workflow_id");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_schema_version ON "public"."workflow_config_snapshot"("schema_version");

COMMIT;
