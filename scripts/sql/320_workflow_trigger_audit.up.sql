BEGIN;

-- Create Sequence for workflow_trigger_audit
CREATE SEQUENCE IF NOT EXISTS id_seq_workflow_trigger_audit;

-- Table Definition: workflow_trigger_audit
CREATE TABLE IF NOT EXISTS "public"."workflow_trigger_audit" (
    "id"                    int             NOT NULL DEFAULT nextval('id_seq_workflow_trigger_audit'::regclass),
    "workflow_id"           int             NOT NULL, -- ci_workflow.id or cd_workflow_runner.id
    "workflow_type"         varchar(20)     NOT NULL, -- CI, PRE_CD, POST_CD
    "pipeline_id"           int             NOT NULL,
    "app_id"                int             NOT NULL,
    "environment_id"        int,            -- null for CI
    "artifact_id"           int,            -- ci_artifact.id
    "trigger_type"          varchar(20)     NOT NULL, -- MANUAL, AUTO, WEBHOOK
    "triggered_by"          int4            NOT NULL,
    "trigger_metadata"      jsonb,          -- additional trigger context
    "workflow_config_snapshot_id" int       NOT NULL, -- reference to workflow_config_snapshot
    "infra_config_snapshot_id"    int,      -- reference to existing infra_config_trigger_history
    "status"                varchar(20)     NOT NULL DEFAULT 'SAVED', -- SAVED
    "created_on"            timestamptz     NOT NULL,
    "created_by"            int4            NOT NULL,
    "updated_on"            timestamptz     NOT NULL,
    "updated_by"            int4            NOT NULL,
    PRIMARY KEY ("id"),
    UNIQUE ("workflow_id", "workflow_type")
);

-- Create Sequence for workflow_config_snapshot
CREATE SEQUENCE IF NOT EXISTS id_seq_workflow_config_snapshot;

-- Table Definition: workflow_config_snapshot
CREATE TABLE IF NOT EXISTS "public"."workflow_config_snapshot" (
    "id"                    int             NOT NULL DEFAULT nextval('id_seq_workflow_config_snapshot'::regclass),
    "workflow_type"         varchar(20)     NOT NULL, -- CI, PRE_CD, POST_CD
    "pipeline_id"           int             NOT NULL,
    "app_id"                int             NOT NULL,
    "environment_id"        int,            -- null for CI
    "workflow_request_json" text            NOT NULL, -- complete WorkflowRequest JSON
    "pipeline_config_json"  text            NOT NULL, -- pipeline configuration at trigger time
    "deployment_config_json" text,          -- deployment config for CD workflows
    "git_materials_json"    text,           -- git materials snapshot
    "ci_pipeline_scripts_json" text,        -- CI scripts for CI workflows
    "pre_post_scripts_json" text,           -- pre/post CD scripts for CD workflows
    "config_maps_json"      text,           -- config maps snapshot
    "secrets_json"          text,           -- secrets snapshot
    "strategy_config_json"  text,           -- deployment strategy for CD workflows
    "created_on"            timestamptz     NOT NULL,
    "created_by"            int4            NOT NULL,
    "updated_on"            timestamptz     NOT NULL,
    "updated_by"            int4            NOT NULL,
    PRIMARY KEY ("id")
);

-- Create Sequence for trigger_material_snapshot
CREATE SEQUENCE IF NOT EXISTS id_seq_trigger_material_snapshot;

-- Table Definition: trigger_material_snapshot
CREATE TABLE IF NOT EXISTS "public"."trigger_material_snapshot" (
    "id"                    int             NOT NULL DEFAULT nextval('id_seq_trigger_material_snapshot'::regclass),
    "workflow_trigger_audit_id" int         NOT NULL,
    "git_material_id"       int,
    "commit_hash"           varchar(255),
    "commit_message"        text,
    "commit_author"         varchar(255),
    "commit_date"           timestamptz,
    "branch"                varchar(255),
    "tag"                   varchar(255),
    "webhook_data"          jsonb,          -- webhook payload if triggered by webhook
    "created_on"            timestamptz     NOT NULL,
    "created_by"            int4            NOT NULL,
    "updated_on"            timestamptz     NOT NULL,
    "updated_by"            int4            NOT NULL,
    PRIMARY KEY ("id"),
    FOREIGN KEY ("workflow_trigger_audit_id") REFERENCES "public"."workflow_trigger_audit"("id")
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_workflow_trigger_audit_pipeline_id ON "public"."workflow_trigger_audit"("pipeline_id");
CREATE INDEX IF NOT EXISTS idx_workflow_trigger_audit_app_id ON "public"."workflow_trigger_audit"("app_id");
CREATE INDEX IF NOT EXISTS idx_workflow_trigger_audit_workflow_type ON "public"."workflow_trigger_audit"("workflow_type");
CREATE INDEX IF NOT EXISTS idx_workflow_trigger_audit_created_on ON "public"."workflow_trigger_audit"("created_on");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_pipeline_id ON "public"."workflow_config_snapshot"("pipeline_id");
CREATE INDEX IF NOT EXISTS idx_workflow_config_snapshot_app_id ON "public"."workflow_config_snapshot"("app_id");
CREATE INDEX IF NOT EXISTS idx_trigger_material_snapshot_audit_id ON "public"."trigger_material_snapshot"("workflow_trigger_audit_id");

COMMIT;
