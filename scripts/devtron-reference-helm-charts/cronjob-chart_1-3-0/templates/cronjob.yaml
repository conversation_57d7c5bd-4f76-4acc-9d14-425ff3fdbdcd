  {{- $hasCMEnvExists := false -}}
  {{- $hasCMVolumeExists := false -}}
  {{- if .Values.ConfigMaps.enabled }}
  {{- range .Values.ConfigMaps.maps }}
  {{- if eq .type "volume"}}
  {{- $hasCMVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasCMEnvExists = true}}
  {{- end }}
  {{- end }}
  {{- end }}

  {{- $hasSecretEnvExists := false -}}
  {{- $hasSecretVolumeExists := false -}}
  {{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{- if eq .type "volume"}}
  {{- $hasSecretVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasSecretEnvExists = true }}
  {{- end }}
  {{- end }}
  {{- end }}

{{ if eq .Values.kind "CronJob" }}
{{ if semverCompare "<1.21" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: batch/v1beta1
{{- else }}
apiVersion: batch/v1
{{- end }}
kind: CronJob
metadata:
  name: {{ include ".Chart.Name .fullname" $ }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
spec:
  schedule: {{ $.Values.cronjobConfigs.schedule | quote }}
  startingDeadlineSeconds: {{ $.Values.cronjobConfigs.startingDeadlineSeconds }}
  concurrencyPolicy: {{ $.Values.cronjobConfigs.concurrencyPolicy }}
  {{ if semverCompare ">1.20" .Capabilities.KubeVersion.GitVersion -}}
  suspend: {{ $.Values.cronjobConfigs.suspend }}
  {{- end }}
  successfulJobsHistoryLimit: {{ $.Values.cronjobConfigs.successfulJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ $.Values.cronjobConfigs.failedJobsHistoryLimit }}
  jobTemplate:
    spec:
      parallelism: {{ $.Values.cronjobConfigs.parallelism }}
      template:
        metadata:
        {{- if .Values.podAnnotations }}
          annotations:
          {{- range $key, $value := .Values.podAnnotations }}
            {{ $key }}: {{ $value | quote }}
          {{- end }}
        {{- end }}
          labels:
            app: {{ template ".Chart.Name .name" $ }}
            appId: {{ $.Values.app | quote }}
            envId: {{ $.Values.env | quote }}
            release: {{ $.Release.Name }}
    {{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 12 }}
    {{- end }}
        spec:
          shareProcessNamespace: {{ $.Values.shareProcessNamespace }}
          terminationGracePeriodSeconds: {{ $.Values.GracePeriod }}
          restartPolicy: {{ $.Values.cronjobConfigs.restartPolicy }}
    {{- if and $.Values.Spec.Affinity.Key $.Values.Spec.Affinity.Values }}
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: {{ $.Values.Spec.Affinity.Key  }}
                    operator: In
                    values:
                    - {{ $.Values.Spec.Affinity.Values | default "nodes"  }}
    {{- end }}
    {{- if $.Values.serviceAccountName }}
          serviceAccountName: {{ $.Values.serviceAccountName }}
    {{- end }}
      {{- if .Values.tolerations }}
          tolerations:
    {{- toYaml .Values.tolerations | nindent 12 }}
      {{- end }}
    {{- if $.Values.podSecurityContext }}
          securityContext:
    {{- toYaml .Values.podSecurityContext | nindent 12 }}
    {{- end }}      
    {{- if $.Values.imagePullSecrets}}
          imagePullSecrets:
      {{- range .Values.imagePullSecrets }}
            - name: {{ . }}
      {{- end }}
    {{- end}}
    {{- if $.Values.initContainers}}
          initContainers:
    {{- range $i, $c := .Values.initContainers }}
    {{- if .reuseContainerImage}}
            - name: {{ $.Chart.Name }}-init-{{ add1 $i }}
              image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
              imagePullPolicy: {{ $.Values.image.pullPolicy }}
    {{- if .command}}
              command:
    {{- toYaml .command | nindent 16 -}}
    {{- end}}
    {{- if .resources}}
              resources:
    {{- toYaml .resources | nindent 16 -}}
    {{- end}}
    {{- if .volumeMounts}}
              volumeMounts:
    {{- toYaml .volumeMounts | nindent 16 -}}
    {{- end}}
    {{- else}}
            -
    {{- toYaml $c | nindent 14 -}}
    {{- end}}
    {{- end}}
    {{- end}}
          containers:
    {{- if $.Values.containers }}
    {{- toYaml $.Values.containers | nindent 12 -}}
    {{- end}}
            - name: {{ $.Chart.Name }}
              image: "{{ .Values.server.deployment.image }}:{{ .Values.server.deployment.image_tag }}"
              imagePullPolicy: {{ $.Values.image.pullPolicy }}
    {{- if $.Values.privileged }}
              securityContext:
                privileged: true
    {{- end}}
    {{- if $.Values.containerSecurityContext }}
              securityContext:
    {{- toYaml .Values.containerSecurityContext | nindent 16 }}
    {{- end }}
    {{- if and $.Values.containerSecurityContext $.Values.privileged }}
              securityContext:
                privileged: true
    {{- toYaml .Values.containerSecurityContext | nindent 16 }}
    {{- end }}    
              ports:
              {{- range $.Values.ContainerPort }}
                - name: {{ .name}}
                  containerPort: {{ .port  }}
                  protocol: TCP
              {{- end}}
    {{- if and $.Values.command.value $.Values.command.enabled}}
              command:
    {{- toYaml $.Values.command.value | nindent 16 -}}
    {{- end}}
    {{- if and $.Values.args.value $.Values.args.enabled}}
              args:
    {{- toYaml $.Values.args.value | nindent 16 -}}
    {{- end }}
              env:
                - name: CONFIG_HASH
                  value: {{ include (print $.Chart.Name "/templates/configmap.yaml") . | sha256sum }}
                - name: SECRET_HASH
                  value: {{ include (print $.Chart.Name "/templates/secret.yaml") . | sha256sum }}
                - name: DEVTRON_APP_NAME
                  value: {{ template ".Chart.Name .name" $ }}
                - name: POD_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.name
              {{- range $.Values.EnvVariablesFromFieldPath }}
                - name: {{ .name }}
                  valueFrom:
                    fieldRef:
                      fieldPath: {{ .fieldPath }}
              {{- end}}
              {{- range $.Values.EnvVariables }}
                - name: {{ .name}}
                  value: {{ .value | quote }}
              {{- end}}
              {{- if or (and ($hasCMEnvExists) (.Values.ConfigMaps.enabled)) (and ($hasSecretEnvExists) (.Values.ConfigSecrets.enabled)) }}
              envFrom:
              {{- if .Values.ConfigMaps.enabled }}
              {{- range .Values.ConfigMaps.maps }}
              {{- if eq .type "environment" }}
              - configMapRef:
                  {{- if eq .external true }}
                  name: {{ .name }}
                  {{- else if eq .external false }}
                  name: {{ .name}}-{{ $.Values.app }}
                  {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- if .Values.ConfigSecrets.enabled }}
              {{- range .Values.ConfigSecrets.secrets }}
              {{- if eq .type "environment" }}
              - secretRef:
                  {{if eq .external true}}
                  name: {{ .name }}
                  {{- else if eq .external false}}
                  name: {{ .name}}-{{ $.Values.app }}
                  {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}

              resources:
    {{- toYaml $.Values.resources | trim | nindent 16 }}
              volumeMounts:
    {{- with .Values.volumeMounts }}
    {{- toYaml . | trim | nindent 16 }}
    {{- end }}
              {{- if .Values.ConfigMaps.enabled }}
              {{- range .Values.ConfigMaps.maps }}
              {{- if eq .type "volume"}}
              {{- $cmName := .name -}}
              {{- $cmMountPath := .mountPath -}}
              {{- if eq .subPath false }}
                - name: {{ $cmName | replace "." "-"}}-vol
                  mountPath: {{ $cmMountPath }}
              {{- else }}
              {{- range $k, $v := .data }}
                - name: {{ $cmName | replace "." "-"}}-vol
                  mountPath: {{ $cmMountPath }}/{{ $k}}
                  subPath: {{ $k}}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}

              {{- if .Values.ConfigSecrets.enabled }}
              {{- range .Values.ConfigSecrets.secrets }}
              {{- if eq .type "volume"}}
              {{- $cmName := .name -}}
              {{- $cmMountPath := .mountPath -}}
              {{- if eq .subPath false }}
                - name: {{ $cmName | replace "." "-"}}-vol
                  mountPath: {{ $cmMountPath }}
              {{- else if and (eq (.subPath) true) (eq (.externalType) "KubernetesSecret") }}
              {{- else if and (eq (.subPath) true) (eq (.external) true) }}
              {{- range .secretData }}
                - name: {{ $cmName | replace "." "-"}}-vol
                  mountPath: {{ $cmMountPath}}/{{ .name }}
                  subPath: {{ .name }}
              {{- end }}
              {{- else }}
              {{- range $k, $v := .data }}
                - name: {{ $cmName | replace "." "-"}}-vol
                  mountPath: {{ $cmMountPath}}/{{ $k}}
                  subPath: {{ $k}}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- end }}
              {{- if and (eq (len .Values.volumes) 0) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) }} []{{- end }}
              {{- if and (eq (len .Values.volumeMounts) 0) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) }} []{{- end }}
          volumes:
      {{- if $.Values.appMetrics }}
            - name: envoy-config-volume
              configMap:
                name: sidecar-config-{{ template ".Chart.Name .name" $ }}
      {{- end }}
    {{- with .Values.volumes }}
    {{- toYaml . | trim | nindent 12 }}
    {{- end }}
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "volume"}}
            - name: {{ .name | replace "." "-"}}-vol
              configMap:
                {{- if eq .external true }}
                name: {{ .name }}
                {{- else if eq .external false }}
                name: {{ .name}}-{{ $.Values.app }}
                {{- end }}
                {{- if eq (len .filePermission) 0 }}
                {{- else }}
                defaultMode: {{ .filePermission}}
                {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "volume"}}
            - name: {{ .name | replace "." "-"}}-vol
              secret:
                {{- if eq .external true }}
                secretName: {{ .name }}
                {{- else if eq .external false }}
                secretName: {{ .name}}-{{ $.Values.app }}
                {{- end }}
                {{- if eq (len .filePermission) 0 }}
                {{- else }}
                defaultMode: {{ .filePermission}}
                {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if and (eq (len .Values.volumes) 0) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) (eq (.Values.appMetrics) false) }} []{{- end }}
          {{- if and (eq (len .Values.volumes) 0) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) (eq (.Values.appMetrics) false) }} []{{- end }}
{{ end }}
