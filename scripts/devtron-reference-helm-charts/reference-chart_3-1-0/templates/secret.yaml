{{- if $.Values.secret.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
type: Opaque
data:
{{ toYaml $.Values.secret.data | indent 2 }}
{{- end }}


{{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{if eq .external false}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .name}}
type: Opaque
data:
{{ toYaml .data | trim | indent 2 }}
{{- end}}
{{- end}}
{{- end}}
