{{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{if eq .external true }}
  {{if (or (eq .externalType "ESO_GoogleSecretsManager") (eq .externalType "ESO_AWSSecretsManager") (eq .externalType "ESO_HashiCorpVault") (eq .externalType "ESO_AzureSecretsManager"))}}
{{- if .esoSecretData.secretStore }}
---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: {{ .name}}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  provider:
    {{- toYaml .esoSecretData.secretStore | nindent 4 }}
{{- end }}
---
apiVersion: external-secrets.io/v1beta1 
kind: ExternalSecret
metadata:
  name: {{ .name }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  {{- if .esoSecretData.refreshInterval }}
  refreshInterval: {{ .esoSecretData.refreshInterval }}
  {{- else }}
  refreshInterval: 1h
  {{- end}}
  {{- if and .esoSecretData.secretStoreRef (not .esoSecretData.secretStore) }}
  secretStoreRef:
{{ toYaml .esoSecretData.secretStoreRef | indent 4 }}
  {{- else }}
  secretStoreRef:
    name: {{ .name}}
    kind: SecretStore
  {{- end }}
  target:
    name: {{ .name}}
    {{- if .esoSecretData.template }}
    template:
      {{- toYaml .esoSecretData.template | nindent 6 }}
    {{- end }}    
    creationPolicy: Owner
  {{- if .esoSecretData.esoDataFrom }}
  dataFrom:
    {{- toYaml .esoSecretData.esoDataFrom | nindent 4 }}
  {{- else }}    
  data:
  {{- range .esoSecretData.esoData }}
  - secretKey: {{ .secretKey }}
    remoteRef:
        key: {{ .key }}
        {{- if .property }}
        property: {{ .property }}
        {{- end }}
  {{- end}}
{{- end}}
{{- end}}
{{- end}}
{{- end}}
{{- end}}