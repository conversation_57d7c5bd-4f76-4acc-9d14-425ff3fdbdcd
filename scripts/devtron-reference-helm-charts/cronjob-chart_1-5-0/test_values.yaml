# Default values for myapp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
kind: ScaledJob

jobConfigs:
  backoffLimit: 5
  activeDeadlineSeconds: 100
  parallelism: 1
  completions: 2
  suspend: false
  ttlSecondsAfterFinished: 100

cronjobConfigs:
  schedule: "* * * * *"
  startingDeadlineSeconds: 100
  concurrencyPolicy: Allow
  suspend: false
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  restartPolicy: OnFailure

imagePullSecrets:
  - test1
  - test2
MinReadySeconds: 5
MaxSurge: 1
MaxUnavailable: 0
GracePeriod: 30
ContainerPort:
  - name: app
    port: 8080
    servicePort: 80
    envoyPort: 8799
    useHTTP2: true
    supportStreaming: true
    idleTimeout: 1800s
    servicemonitor:
      enabled: true
      path: /abc
      scheme: 'http'
      interval: 30s
      scrapeTimeout: 20s
      metricRelabelings:
        - sourceLabels: [namespace]
          regex: '(.*)'
          replacement: myapp
          targetLabel: target_namespace
  - name: app1
    port: 8090
    servicePort: 8080
    useGRPC: true
    servicemonitor:
      enabled: true
  - name: app2
    port: 8091
    servicePort: 8081
    useGRPC: true

pauseForSecondsBeforeSwitchActive: 30
waitForSecondsBeforeScalingDown: 30
autoPromotionSeconds: 30

Spec:
  Affinity:
    Key:
    #  Key: kops.k8s.io/instancegroup
    Values:


image:
  pullPolicy: IfNotPresent

secret:
  enabled: false

service:
  enabled: false
  type: ClusterIP
  #  name: "1234567890123456789012345678901234567890123456789012345678901234567890"
  annotations: {}
    # test1: test2
  # test3: test4

server:
  deployment:
    image_tag: 1-95af053
    image: ""
deploymentType: "RECREATE"


EnvVariables:
  - name: FLASK_ENV
    value: qa

prometheus:
  release: monitoring

servicemonitor:
  additionalLabels: {}

kedaAutoscaling:
  envSourceContainerName: "" 
  minReplicaCount: 1 
  maxReplicaCount: 2
  pollingInterval: 30                         
  successfulJobsHistoryLimit: 5              
  failedJobsHistoryLimit: 5
  rolloutStrategy: default
  scalingStrategy:
    strategy: "custom"                        
    customScalingQueueLengthDeduction: 1      
    customScalingRunningJobPercentage: "0.5"  
    pendingPodConditions:                     
      - "Ready"
      - "PodScheduled"
      - "AnyOtherCustomPodCondition"
    multipleScalersCalculation : "max"
  triggers: 
  - type: rabbitmq
    metadata:
      queueName: hello
      host: RabbitMqHost
      queueLength  : '5'
  authenticationRef: {}
  triggerAuthentication:
    enabled: true
    name: ""
    spec: {}

prometheusRule:
  enabled: true
  additionalLabels: {}
  namespace: ""
  rules:
    # These are just examples rules, please adapt them to your needs
    - alert: TooMany500s
      expr: 100 * ( sum( nginx_ingress_controller_requests{status=~"5.+"} ) / sum(nginx_ingress_controller_requests) ) > 5
      for: 1m
      labels:
        severity: critical
      annotations:
        description: Too many 5XXs
        summary: More than 5% of the all requests did return 5XX, this require your attention
    - alert: TooMany400s
      expr: 100 * ( sum( nginx_ingress_controller_requests{status=~"4.+"} ) / sum(nginx_ingress_controller_requests) ) > 5
      for: 1m
      labels:
        severity: critical
      annotations:
        description: Too many 4XXs
        summary: More than 5% of the all requests did return 4XX, this require your attention

command:
  enabled: true
  value:
    - /bin/sh
    
args:
  enabled: false
  value: []

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1
    memory: 200Mi
  requests:
    cpu: 0.10
    memory: 100Mi
volumeMounts:
  - name: log-volume
    mountPath: /var/log

volumes:
  - name: log-volume
    emptyDir: {}


nodeSelector: {}


#used for deployment algo selection
orchestrator.deploymant.algo: 1

ConfigMaps:
  enabled: false
  # maps:
  # - name: config-map-1
  #   type: environment
  #   external: false
  #   data:
  #     key1: key1value-1
  #     key2: key2value-1
  #     key3: key3value-1
  # - name: config-map-2
  #   type: volume
  #   external: false
  #   mountPath: /etc/config/2
  #   subPath: false
  #   filePermission: "777"
  #   data:
  #     key1: |
  #       club : manchester utd
  #       nation : england
  #     key2: abc-2
  #     key3: abc-2
  # - name: config-map-3
  #   type: environment
  #   external: true
  #   mountPath: /etc/config/3
  #   data: []
  # - name: config-map-4
  #   type: volume
  #   external: true
  #   mountPath: /etc/config/4
  #   data: []


ConfigSecrets:
  enabled: false
  secrets:
  - name: config-secret-1
    type: environment
    external: true
    externalType: ESO_AWSSecretsManager
    esoSecretData:
      secretStore:
        aws:
          service: SecretsManager
          region: us-east-1
          auth:
            secretRef:
              accessKeyIDSecretRef:
                name: awssm-secret
                key: access-key
              secretAccessKeySecretRef:
                name: awssm-secret
                key: secret-access-key
      esoData:
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
    data:
      key1: key1value-1
      key2: key2value-1
      key3: key3value-1
  # - name: config-secret-1
  #   type: environment
  #   external: false
  #   data:
  #     key1: key1value-1
  #     key2: key2value-1
  #     key3: key3value-1
  # - name: config-secret-2x
  #   type: volume
  #   external: false
  #   mountPath: /etc/config/2
  #   subPath: false
  #   filePermission: "777"
  #   data:
  #     key1: |
  #       club : manchester utd
  #       nation : england
  #     key2: abc-2
  #     key3: abc-2


initContainers:
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage.
  ## If reuse container image is set as true, you cannot specify an addition init container along with the image.
  # - reuseContainerImage: true
  #   volumeMounts:
  #     - mountPath: /etc/ls-oms
  #       name: ls-oms-cm-vol
  #   command:
  #     - flyway
  #     - -configFiles=/etc/ls-oms/flyway.conf
  #     - migrate

  # - name: volume-mount-hack
  #   image: busybox
  #   command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #   volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data
  #   resources:
  #     limits:
  #       cpu: 50m
  #       memory: 100Mi
  #     requests:
  #       cpu: 10m
  #       memory: 50Mi
  - name: volume-mount-hack2
    image: busybox
    command: ["sh", "-c", "chown -R 1000:1000 logs"]
    volumeMounts:
     - mountPath: /usr/local/airflow/logs
       name: logs-data
    resources:
      limits:
        cpu: 50m
        memory: 100Mi
      requests:
        cpu: 10m
        memory: 50Mi

containers:
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage .
  # - name: volume-mount-hack
  #   image: busybox
  #   command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #   volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data


rawYaml: []
# - apiVersion: v1
#   kind: Service
#   metadata:
#    annotations:
#    labels:
#     app: sample-metrics-app
#    name: sample-metrics-app
#    namespace: default
#   spec:
#    ports:
#     - name: web
#       port: 80
#       protocol: TCP
#       targetPort: 8080
#    selector:
#     app: sample-metrics-app
#    sessionAffinity: None
#    type: ClusterIP
# - apiVersion: v1
#   kind: Service
#   metadata:
#    annotations:
#    labels:
#     app: sample-metrics-app
#    name: sample-metrics-app
#    namespace: default
#   spec:
#    ports:
#     - name: web
#       port: 80
#       protocol: TCP
#       targetPort: 8080
#    selector:
#     app: sample-metrics-app
#    sessionAffinity: None
#    type: ClusterIP

podDisruptionBudget: {}
  #  minAvailable: 1
  #  maxUnavailable: 1

  ## Node tolerations for server scheduling to nodes with taints
  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
##

tolerations: []
  # - key: "key"
  #   operator: "Equal|Exists"
  #   value: "value"
  #   effect: "NoSchedule|PreferNoSchedule|NoExecute(1.6 only)"

appMetrics: false

podAnnotations:
  fluentbit.io/exclude: true

podLabels:
  severity: critical

containerSecurityContext:
  allowPrivilegeEscalation: false
  capabilities:
    add:
    - SYS_PTRACE


podSecurityContext: 
  runAsUser: 1000
  runAsGroup: 3000
  fsGroup: 2000 

shareProcessNamespace: true
