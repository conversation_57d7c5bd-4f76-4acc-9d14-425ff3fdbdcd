# Source: postgresql/templates/initialization-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-postgresql-init-scripts
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
data:
  db_create.sql: |
    create database casbin;
    create database git_sensor;
    create database lens;
    create database clairv4;
---
# Source: postgresql/templates/metrics-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgresql-postgresql-metrics
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
  annotations:
    prometheus.io/port: "9187"
    prometheus.io/scrape: "true"
spec:
  type: ClusterIP
  ports:
    - name: http-metrics
      port: 9187
      targetPort: http-metrics
  selector:
    app: postgresql
    release: devtron
    role: master
---
# Source: postgresql/templates/svc-headless.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgresql-postgresql-headless
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: tcp-postgresql
      port: 5432
      targetPort: tcp-postgresql
  selector:
    app: postgresql
    release: "devtron"
---
# Source: postgresql/templates/svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgresql-postgresql
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
spec:
  type: NodePort
  ports:
    - name: tcp-postgresql
      nodePort: 30468
      port: 5432
      targetPort: tcp-postgresql
  selector:
    app: postgresql
    release: "devtron"
    role: master
---
# Source: postgresql/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-postgresql
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
spec:
  serviceName: postgresql-postgresql-headless
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: postgresql
      release: "devtron"
      role: master
  template:
    metadata:
      name: postgresql-postgresql
      labels:
        app: postgresql
        chart: postgresql-8.6.4
        release: "devtron"
        role: master
    spec:
      securityContext:
        fsGroup: 1001
      initContainers:
        - name: init-chmod-data
          image: "quay.io/devtron/minideb:latest"
          imagePullPolicy: "IfNotPresent"
          command:
            - /bin/sh
            - -cx
            - |

              mkdir -p /bitnami/postgresql/data
              chmod 700 /bitnami/postgresql/data
              find /bitnami/postgresql -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | \
                xargs chown -R 1001:1001
              chmod -R 777 /dev/shm
          securityContext:
            runAsUser: 0
          volumeMounts:

            - name: data
              mountPath: /bitnami/postgresql
              subPath:
            - name: dshm
              mountPath: /dev/shm
        # - name: do-something
        #   image: busybox
        #   command: ['do', 'something']

      containers:
        - name: postgresql-postgresql
          image: quay.io/devtron/postgres:11.9.0-debian-10-r26
          imagePullPolicy: "IfNotPresent"
          securityContext:
            runAsUser: 1001
          env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: POSTGRESQL_PORT_NUMBER
              value: "5432"
            - name: POSTGRESQL_VOLUME_DIR
              value: "/bitnami/postgresql"
            - name: PGDATA
              value: "/bitnami/postgresql/data"
            - name: POSTGRES_USER
              value: "postgres"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-postgresql
                  key: postgresql-password
            - name: POSTGRES_DB
              value: "orchestrator"
            - name: POSTGRESQL_ENABLE_LDAP
              value: "no"
            - name: POSTGRESQL_SHARED_PRELOAD_LIBRARIES
              value: pgaudit, uuid-ossp
          ports:
            - name: tcp-postgresql
              containerPort: 5432
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "postgres" -d "orchestrator" -h 127.0.0.1 -p 5432
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - -e
                - |
                  exec pg_isready -U "postgres" -d "orchestrator" -h 127.0.0.1 -p 5432
                  [ -f /opt/bitnami/postgresql/tmp/.initialized ] || [ -f /bitnami/postgresql/.initialized ]
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          volumeMounts:
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d/
            - name: dshm
              mountPath: /dev/shm
            - name: data
              mountPath: /bitnami/postgresql
              subPath:
        - name: metrics
          image: quay.io/devtron/postgres_exporter:v0.4.7
          imagePullPolicy: "IfNotPresent"
          env:
            - name: DATA_SOURCE_URI
              value: "127.0.0.1:5432/orchestrator?sslmode=disable"
            - name: DATA_SOURCE_PASS
              valueFrom:
                secretKeyRef:
                  name: postgresql-postgresql
                  key: postgresql-password
            - name: DATA_SOURCE_USER
              value: postgres
          livenessProbe:
            httpGet:
              path: /
              port: http-metrics
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            httpGet:
              path: /
              port: http-metrics
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          volumeMounts:
          ports:
            - name: http-metrics
              containerPort: 9187
      volumes:
        - name: custom-init-scripts
          configMap:
            name: postgresql-postgresql-init-scripts
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 1Gi
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "20Gi"