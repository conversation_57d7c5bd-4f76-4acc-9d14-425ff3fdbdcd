// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/internal/sql/repository"
)

// NotificationSettingsRepository is an autogenerated mock type for the NotificationSettingsRepository type
type NotificationSettingsRepository struct {
	mock.Mock
}

// DeleteNotificationSettingsByConfigId provides a mock function with given fields: viewId, tx
func (_m *NotificationSettingsRepository) DeleteNotificationSettingsByConfigId(viewId int, tx *pg.Tx) (int, error) {
	ret := _m.Called(viewId, tx)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) (int, error)); ok {
		return rf(viewId, tx)
	}
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) int); ok {
		r0 = rf(viewId, tx)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int, *pg.Tx) error); ok {
		r1 = rf(viewId, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteNotificationSettingsViewById provides a mock function with given fields: id, tx
func (_m *NotificationSettingsRepository) DeleteNotificationSettingsViewById(id int, tx *pg.Tx) (int, error) {
	ret := _m.Called(id, tx)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) (int, error)); ok {
		return rf(id, tx)
	}
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) int); ok {
		r0 = rf(id, tx)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int, *pg.Tx) error); ok {
		r1 = rf(id, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchNotificationSettingGroupBy provides a mock function with given fields: viewId
func (_m *NotificationSettingsRepository) FetchNotificationSettingGroupBy(viewId int) ([]repository.NotificationSettings, error) {
	ret := _m.Called(viewId)

	var r0 []repository.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]repository.NotificationSettings, error)); ok {
		return rf(viewId)
	}
	if rf, ok := ret.Get(0).(func(int) []repository.NotificationSettings); ok {
		r0 = rf(viewId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(viewId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAll provides a mock function with given fields: offset, size
func (_m *NotificationSettingsRepository) FindAll(offset int, size int) ([]*repository.NotificationSettingsView, error) {
	ret := _m.Called(offset, size)

	var r0 []*repository.NotificationSettingsView
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*repository.NotificationSettingsView, error)); ok {
		return rf(offset, size)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*repository.NotificationSettingsView); ok {
		r0 = rf(offset, size)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.NotificationSettingsView)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(offset, size)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNSViewCount provides a mock function with given fields:
func (_m *NotificationSettingsRepository) FindNSViewCount() (int, error) {
	ret := _m.Called()

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingBuildOptions provides a mock function with given fields: settingRequest
func (_m *NotificationSettingsRepository) FindNotificationSettingBuildOptions(settingRequest *repository.SearchRequest) ([]*repository.SettingOptionDTO, error) {
	ret := _m.Called(settingRequest)

	var r0 []*repository.SettingOptionDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SearchRequest) ([]*repository.SettingOptionDTO, error)); ok {
		return rf(settingRequest)
	}
	if rf, ok := ret.Get(0).(func(*repository.SearchRequest) []*repository.SettingOptionDTO); ok {
		r0 = rf(settingRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SettingOptionDTO)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SearchRequest) error); ok {
		r1 = rf(settingRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingDeploymentOptions provides a mock function with given fields: settingRequest
func (_m *NotificationSettingsRepository) FindNotificationSettingDeploymentOptions(settingRequest *repository.SearchRequest) ([]*repository.SettingOptionDTO, error) {
	ret := _m.Called(settingRequest)

	var r0 []*repository.SettingOptionDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SearchRequest) ([]*repository.SettingOptionDTO, error)); ok {
		return rf(settingRequest)
	}
	if rf, ok := ret.Get(0).(func(*repository.SearchRequest) []*repository.SettingOptionDTO); ok {
		r0 = rf(settingRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SettingOptionDTO)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SearchRequest) error); ok {
		r1 = rf(settingRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingsByConfigIdAndConfigType provides a mock function with given fields: configId, configType
func (_m *NotificationSettingsRepository) FindNotificationSettingsByConfigIdAndConfigType(configId int, configType string) ([]*repository.NotificationSettings, error) {
	ret := _m.Called(configId, configType)

	var r0 []*repository.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]*repository.NotificationSettings, error)); ok {
		return rf(configId, configType)
	}
	if rf, ok := ret.Get(0).(func(int, string) []*repository.NotificationSettings); ok {
		r0 = rf(configId, configType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(configId, configType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingsByViewId provides a mock function with given fields: viewId
func (_m *NotificationSettingsRepository) FindNotificationSettingsByViewId(viewId int) ([]repository.NotificationSettings, error) {
	ret := _m.Called(viewId)

	var r0 []repository.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]repository.NotificationSettings, error)); ok {
		return rf(viewId)
	}
	if rf, ok := ret.Get(0).(func(int) []repository.NotificationSettings); ok {
		r0 = rf(viewId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(viewId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingsViewById provides a mock function with given fields: id
func (_m *NotificationSettingsRepository) FindNotificationSettingsViewById(id int) (*repository.NotificationSettingsView, error) {
	ret := _m.Called(id)

	var r0 *repository.NotificationSettingsView
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.NotificationSettingsView, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.NotificationSettingsView); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.NotificationSettingsView)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNotificationSettingsViewByIds provides a mock function with given fields: id
func (_m *NotificationSettingsRepository) FindNotificationSettingsViewByIds(id []*int) ([]*repository.NotificationSettingsView, error) {
	ret := _m.Called(id)

	var r0 []*repository.NotificationSettingsView
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*repository.NotificationSettingsView, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*repository.NotificationSettingsView); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.NotificationSettingsView)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveAllNotificationSettings provides a mock function with given fields: notificationSettings, tx
func (_m *NotificationSettingsRepository) SaveAllNotificationSettings(notificationSettings []repository.NotificationSettings, tx *pg.Tx) (int, error) {
	ret := _m.Called(notificationSettings, tx)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func([]repository.NotificationSettings, *pg.Tx) (int, error)); ok {
		return rf(notificationSettings, tx)
	}
	if rf, ok := ret.Get(0).(func([]repository.NotificationSettings, *pg.Tx) int); ok {
		r0 = rf(notificationSettings, tx)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func([]repository.NotificationSettings, *pg.Tx) error); ok {
		r1 = rf(notificationSettings, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveNotificationSetting provides a mock function with given fields: notificationSettings, tx
func (_m *NotificationSettingsRepository) SaveNotificationSetting(notificationSettings *repository.NotificationSettings, tx *pg.Tx) (*repository.NotificationSettings, error) {
	ret := _m.Called(notificationSettings, tx)

	var r0 *repository.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettings, *pg.Tx) (*repository.NotificationSettings, error)); ok {
		return rf(notificationSettings, tx)
	}
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettings, *pg.Tx) *repository.NotificationSettings); ok {
		r0 = rf(notificationSettings, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.NotificationSettings, *pg.Tx) error); ok {
		r1 = rf(notificationSettings, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveNotificationSettingsConfig provides a mock function with given fields: notificationSettingsView, tx
func (_m *NotificationSettingsRepository) SaveNotificationSettingsConfig(notificationSettingsView *repository.NotificationSettingsView, tx *pg.Tx) (*repository.NotificationSettingsView, error) {
	ret := _m.Called(notificationSettingsView, tx)

	var r0 *repository.NotificationSettingsView
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettingsView, *pg.Tx) (*repository.NotificationSettingsView, error)); ok {
		return rf(notificationSettingsView, tx)
	}
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettingsView, *pg.Tx) *repository.NotificationSettingsView); ok {
		r0 = rf(notificationSettingsView, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.NotificationSettingsView)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.NotificationSettingsView, *pg.Tx) error); ok {
		r1 = rf(notificationSettingsView, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateNotificationSettings provides a mock function with given fields: notificationSettings, tx
func (_m *NotificationSettingsRepository) UpdateNotificationSettings(notificationSettings *repository.NotificationSettings, tx *pg.Tx) (*repository.NotificationSettings, error) {
	ret := _m.Called(notificationSettings, tx)

	var r0 *repository.NotificationSettings
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettings, *pg.Tx) (*repository.NotificationSettings, error)); ok {
		return rf(notificationSettings, tx)
	}
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettings, *pg.Tx) *repository.NotificationSettings); ok {
		r0 = rf(notificationSettings, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.NotificationSettings)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.NotificationSettings, *pg.Tx) error); ok {
		r1 = rf(notificationSettings, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateNotificationSettingsView provides a mock function with given fields: notificationSettingsView, tx
func (_m *NotificationSettingsRepository) UpdateNotificationSettingsView(notificationSettingsView *repository.NotificationSettingsView, tx *pg.Tx) (*repository.NotificationSettingsView, error) {
	ret := _m.Called(notificationSettingsView, tx)

	var r0 *repository.NotificationSettingsView
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettingsView, *pg.Tx) (*repository.NotificationSettingsView, error)); ok {
		return rf(notificationSettingsView, tx)
	}
	if rf, ok := ret.Get(0).(func(*repository.NotificationSettingsView, *pg.Tx) *repository.NotificationSettingsView); ok {
		r0 = rf(notificationSettingsView, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.NotificationSettingsView)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.NotificationSettingsView, *pg.Tx) error); ok {
		r1 = rf(notificationSettingsView, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewNotificationSettingsRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewNotificationSettingsRepository creates a new instance of NotificationSettingsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewNotificationSettingsRepository(t mockConstructorTestingTNewNotificationSettingsRepository) *NotificationSettingsRepository {
	mock := &NotificationSettingsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
