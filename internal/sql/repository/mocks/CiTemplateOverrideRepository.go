// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	mock "github.com/stretchr/testify/mock"
)

// CiTemplateOverrideRepository is an autogenerated mock type for the CiTemplateOverrideRepository type
type CiTemplateOverrideRepository struct {
	mock.Mock
}

func (_m *CiTemplateOverrideRepository) FindByCiPipelineIds(ciPipelineIds []int) ([]*pipelineConfig.CiTemplateOverride, error) {
	return nil, nil
}

// FindByAppId provides a mock function with given fields: appId
func (_m *CiTemplateOverrideRepository) FindByAppId(appId int) ([]*pipelineConfig.CiTemplateOverride, error) {
	ret := _m.Called(appId)

	var r0 []*pipelineConfig.CiTemplateOverride
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiTemplateOverride); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiTemplateOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *CiTemplateOverrideRepository) FindByCiPipelineId(ciPipelineId int) (*pipelineConfig.CiTemplateOverride, error) {
	ret := _m.Called(ciPipelineId)

	var r0 *pipelineConfig.CiTemplateOverride
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiTemplateOverride); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiTemplateOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: templateOverrideConfig
func (_m *CiTemplateOverrideRepository) Save(templateOverrideConfig *pipelineConfig.CiTemplateOverride) (*pipelineConfig.CiTemplateOverride, error) {
	ret := _m.Called(templateOverrideConfig)

	var r0 *pipelineConfig.CiTemplateOverride
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiTemplateOverride) *pipelineConfig.CiTemplateOverride); ok {
		r0 = rf(templateOverrideConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiTemplateOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*pipelineConfig.CiTemplateOverride) error); ok {
		r1 = rf(templateOverrideConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: templateOverrideConfig
func (_m *CiTemplateOverrideRepository) Update(templateOverrideConfig *pipelineConfig.CiTemplateOverride) (*pipelineConfig.CiTemplateOverride, error) {
	ret := _m.Called(templateOverrideConfig)

	var r0 *pipelineConfig.CiTemplateOverride
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiTemplateOverride) *pipelineConfig.CiTemplateOverride); ok {
		r0 = rf(templateOverrideConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiTemplateOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*pipelineConfig.CiTemplateOverride) error); ok {
		r1 = rf(templateOverrideConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewCiTemplateOverrideRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewCiTemplateOverrideRepository creates a new instance of CiTemplateOverrideRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCiTemplateOverrideRepository(t mockConstructorTestingTNewCiTemplateOverrideRepository) *CiTemplateOverrideRepository {
	mock := &CiTemplateOverrideRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
