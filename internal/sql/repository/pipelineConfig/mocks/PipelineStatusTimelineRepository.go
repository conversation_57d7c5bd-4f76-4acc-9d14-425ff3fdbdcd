// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/timelineStatus"
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
)

// PipelineStatusTimelineRepository is an autogenerated mock type for the PipelineStatusTimelineRepository type
type PipelineStatusTimelineRepository struct {
	mock.Mock
}

// CheckIfTerminalStatusTimelinePresentByWfrId provides a mock function with given fields: wfrId
func (_m *PipelineStatusTimelineRepository) CheckIfTerminalStatusTimelinePresentByWfrId(wfrId int) (bool, error) {
	ret := _m.Called(wfrId)

	var r0 bool
	if rf, ok := ret.Get(0).(func(int) bool); ok {
		r0 = rf(wfrId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(wfrId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteByCdWfrIdAndTimelineStatuses provides a mock function with given fields: cdWfrId, status
func (_m *PipelineStatusTimelineRepository) DeleteByCdWfrIdAndTimelineStatuses(cdWfrId int, status []timelineStatus.TimelineStatus) error {
	ret := _m.Called(cdWfrId, status)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, []timelineStatus.TimelineStatus) error); ok {
		r0 = rf(cdWfrId, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteByCdWfrIdAndTimelineStatusesWithTxn provides a mock function with given fields: cdWfrId, status, tx
func (_m *PipelineStatusTimelineRepository) DeleteByCdWfrIdAndTimelineStatusesWithTxn(cdWfrId int, status []timelineStatus.TimelineStatus, tx *pg.Tx) error {
	ret := _m.Called(cdWfrId, status, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, []timelineStatus.TimelineStatus, *pg.Tx) error); ok {
		r0 = rf(cdWfrId, status, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FetchLatestTimelineByAppIdAndEnvId provides a mock function with given fields: appId, envId
func (_m *PipelineStatusTimelineRepository) FetchLatestTimelineByAppIdAndEnvId(appId int, envId int) (*pipelineConfig.PipelineStatusTimeline, error) {
	ret := _m.Called(appId, envId)

	var r0 *pipelineConfig.PipelineStatusTimeline
	if rf, ok := ret.Get(0).(func(int, int) *pipelineConfig.PipelineStatusTimeline); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.PipelineStatusTimeline)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchLatestTimelineByWfrId provides a mock function with given fields: wfrId
func (_m *PipelineStatusTimelineRepository) FetchLatestTimelineByWfrId(wfrId int) (*pipelineConfig.PipelineStatusTimeline, error) {
	ret := _m.Called(wfrId)

	var r0 *pipelineConfig.PipelineStatusTimeline
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.PipelineStatusTimeline); ok {
		r0 = rf(wfrId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.PipelineStatusTimeline)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(wfrId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTimelineByWfrIdAndStatus provides a mock function with given fields: wfrId, status
func (_m *PipelineStatusTimelineRepository) FetchTimelineByWfrIdAndStatus(wfrId int, status timelineStatus.TimelineStatus) (*pipelineConfig.PipelineStatusTimeline, error) {
	ret := _m.Called(wfrId, status)

	var r0 *pipelineConfig.PipelineStatusTimeline
	if rf, ok := ret.Get(0).(func(int, timelineStatus.TimelineStatus) *pipelineConfig.PipelineStatusTimeline); ok {
		r0 = rf(wfrId, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.PipelineStatusTimeline)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, timelineStatus.TimelineStatus) error); ok {
		r1 = rf(wfrId, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTimelinesByPipelineId provides a mock function with given fields: pipelineId
func (_m *PipelineStatusTimelineRepository) FetchTimelinesByPipelineId(pipelineId int) ([]*pipelineConfig.PipelineStatusTimeline, error) {
	ret := _m.Called(pipelineId)

	var r0 []*pipelineConfig.PipelineStatusTimeline
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.PipelineStatusTimeline); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.PipelineStatusTimeline)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTimelinesByWfrId provides a mock function with given fields: wfrId
func (_m *PipelineStatusTimelineRepository) FetchTimelinesByWfrId(wfrId int) ([]*pipelineConfig.PipelineStatusTimeline, error) {
	ret := _m.Called(wfrId)

	var r0 []*pipelineConfig.PipelineStatusTimeline
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.PipelineStatusTimeline); ok {
		r0 = rf(wfrId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.PipelineStatusTimeline)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(wfrId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveTimeline provides a mock function with given fields: timeline
func (_m *PipelineStatusTimelineRepository) SaveTimeline(timeline *pipelineConfig.PipelineStatusTimeline) error {
	ret := _m.Called(timeline)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.PipelineStatusTimeline) error); ok {
		r0 = rf(timeline)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveTimelinesWithTxn provides a mock function with given fields: timelines, tx
func (_m *PipelineStatusTimelineRepository) SaveTimelinesWithTxn(timelines []*pipelineConfig.PipelineStatusTimeline, tx *pg.Tx) error {
	ret := _m.Called(timelines, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func([]*pipelineConfig.PipelineStatusTimeline, *pg.Tx) error); ok {
		r0 = rf(timelines, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTimeline provides a mock function with given fields: timeline
func (_m *PipelineStatusTimelineRepository) UpdateTimeline(timeline *pipelineConfig.PipelineStatusTimeline) error {
	ret := _m.Called(timeline)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.PipelineStatusTimeline) error); ok {
		r0 = rf(timeline)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTimelinesWithTxn provides a mock function with given fields: timelines, tx
func (_m *PipelineStatusTimelineRepository) UpdateTimelinesWithTxn(timelines []*pipelineConfig.PipelineStatusTimeline, tx *pg.Tx) error {
	ret := _m.Called(timelines, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func([]*pipelineConfig.PipelineStatusTimeline, *pg.Tx) error); ok {
		r0 = rf(timelines, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewPipelineStatusTimelineRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewPipelineStatusTimelineRepository creates a new instance of PipelineStatusTimelineRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPipelineStatusTimelineRepository(t mockConstructorTestingTNewPipelineStatusTimelineRepository) *PipelineStatusTimelineRepository {
	mock := &PipelineStatusTimelineRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
