## v1.1.0

## Bugs
- fix: Changes in labels of ref chart 4.20 (#6178)
- fix: Fix cd-trigger-plugin (#6203)
- fix: Bulk edit fix for global override (#6193)
- fix: Panic handling for get workflow logs (#6168)
- fix: User permissions not merging on same environments (#6167)
- fix: Secret masking fix (#6136)
- fix: Delete git repo without pipeline config (#6148)
- fix: All modules installation fix (#6140)
- fix: Update sql fix buildx (#6096)
## Enhancements
- feat: Support to trigger with same hash in CD trigger plugin  (#6205)
- feat: Patch merge strategy for cm cs and dt (#6115)
- feat: Adds Github actions to add the label using slash command (#6126)
- feat: Fetch all non deprecated plugins in list (#6135)
- feat: Node k8s permissions (#6123)
- feat: App details refactoring (#6104)
## Others
- misc: Main sync develop 20dec (#6210)
- misc: migration for mandatory tag (#6208)
- chore: Read service for team (#6201)
- chore: Oss ent triggerService changes (#6189)
- misc: Release candidate v0.25.0 (#6194)
- chore: Panic fix (#6188)
- chore: Panic fix installed app read service (#6185)
- chore: Runtime params variable refactorings (#6183)
- misc: Asset for plugin (#6184)
- chore: Updating deployment status start and end time (#6171)
- misc: Main sync develop 16dec (#6182)
- chore: Adding logs in deployment trigger flow (#6180)
- misc: Added cd workflow runner read service (#6175)
- misc: Added the token that contains the permissions to read the org members (#6164)
- misc: Refactor-plugin-images migrations (#6108)
- misc: Addign new plugins images (#6145)
- misc: Updated authenticator to c3254b79e386 (#6144)
- misc:  Adding pr validator for fork prs as gh command will not work (#6143)
- misc: Adding validation for public repo (#6137)
- misc: Update pull_request_template.md (#6134)
- misc: Authenticator panic fix (#6131)
- misc: Bulk update cm/cs name reference update fix (#6130)
- misc: Cluster, project, environment read and beans (#6067)
- chore: Use certificates data when InsecureSkipTLSVerify is false for a cluster in case of external run CI (#6100)
- misc: main sync develop nov18 (#6107)
- chore: Implemented Rbac enforcer in batch (#6103)
- misc: Update pr-issue-validator.yaml (#6086)
- misc: Sql query fixes (#6097)
- misc: Kubecon 2024 oss sync (#6094)
- chore: Depandabot fixes (#6070)
- misc: Handler error while while helm deployment (#6082)


