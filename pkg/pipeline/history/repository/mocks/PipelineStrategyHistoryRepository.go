// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"context"
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
)

// PipelineStrategyHistoryRepository is an autogenerated mock type for the PipelineStrategyHistoryRepository type
type PipelineStrategyHistoryRepository struct {
	mock.Mock
}

// CreateHistory provides a mock function with given fields: model
func (_m *PipelineStrategyHistoryRepository) CreateHistory(model *repository.PipelineStrategyHistory) (*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(model)

	var r0 *repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(*repository.PipelineStrategyHistory) *repository.PipelineStrategyHistory); ok {
		r0 = rf(model)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository.PipelineStrategyHistory) error); ok {
		r1 = rf(model)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateHistoryWithTxn provides a mock function with given fields: model, tx
func (_m *PipelineStrategyHistoryRepository) CreateHistoryWithTxn(model *repository.PipelineStrategyHistory, tx *pg.Tx) (*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(model, tx)

	var r0 *repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(*repository.PipelineStrategyHistory, *pg.Tx) *repository.PipelineStrategyHistory); ok {
		r0 = rf(model, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository.PipelineStrategyHistory, *pg.Tx) error); ok {
		r1 = rf(model, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeployedHistoryList provides a mock function with given fields: pipelineId, baseConfigId
func (_m *PipelineStrategyHistoryRepository) GetDeployedHistoryList(pipelineId int, baseConfigId int) ([]*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(pipelineId, baseConfigId)

	var r0 []*repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(int, int) []*repository.PipelineStrategyHistory); ok {
		r0 = rf(pipelineId, baseConfigId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(pipelineId, baseConfigId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeploymentDetailsForDeployedStrategyHistory provides a mock function with given fields: pipelineId
func (_m *PipelineStrategyHistoryRepository) GetDeploymentDetailsForDeployedStrategyHistory(pipelineId int) ([]*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(pipelineId)

	var r0 []*repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(int) []*repository.PipelineStrategyHistory); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetHistoryByPipelineIdAndWfrId provides a mock function with given fields: pipelineId, wfrId
func (_m *PipelineStrategyHistoryRepository) GetHistoryByPipelineIdAndWfrId(ctx context.Context, pipelineId, wfrId int) (*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(pipelineId, wfrId)

	var r0 *repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(int, int) *repository.PipelineStrategyHistory); ok {
		r0 = rf(pipelineId, wfrId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(pipelineId, wfrId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetHistoryForDeployedStrategyById provides a mock function with given fields: id, pipelineId
func (_m *PipelineStrategyHistoryRepository) GetHistoryForDeployedStrategyById(id int, pipelineId int) (*repository.PipelineStrategyHistory, error) {
	ret := _m.Called(id, pipelineId)

	var r0 *repository.PipelineStrategyHistory
	if rf, ok := ret.Get(0).(func(int, int) *repository.PipelineStrategyHistory); ok {
		r0 = rf(id, pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.PipelineStrategyHistory)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(id, pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewPipelineStrategyHistoryRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewPipelineStrategyHistoryRepository creates a new instance of PipelineStrategyHistoryRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPipelineStrategyHistoryRepository(t mockConstructorTestingTNewPipelineStrategyHistoryRepository) *PipelineStrategyHistoryRepository {
	mock := &PipelineStrategyHistoryRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
