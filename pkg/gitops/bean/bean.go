/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bean

type RepositoryCredentialsDto struct {
	Url               string  `json:"url,omitempty"`
	UsernameSecret    *KeyDto `json:"usernameSecret,omitempty"`
	PasswordSecret    *KeyDto `json:"passwordSecret,omitempty"`
	TLSClientCertData *KeyDto `json:"tlsClientCertData,omitempty"`
	TLSClientCertKey  *KeyDto `json:"tlsClientCertKey,omitempty"`
}

type KeyDto struct {
	Name string `json:"name,omitempty"`
	Key  string `json:"key,omitempty"`
}

type GitOpsSecretKey = string

const USERNAME GitOpsSecretKey = "username"
const PASSWORD GitOpsSecretKey = "password"
const TLSKey GitOpsSecretKey = "tlsKey"
const TLSCert GitOpsSecretKey = "tlsCert"
