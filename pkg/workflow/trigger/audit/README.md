# Workflow Trigger Audit System

This package provides a comprehensive audit system for CI, Pre-CD, Post-CD, and Deploy-CD workflow triggers in Devtron. It captures complete workflow configurations at trigger time and enables retrigger functionality using historical snapshots.

## Features

- **Complete Configuration Audit**: Captures all workflow configurations, pipeline settings, git materials, and infrastructure configs at trigger time
- **Retrigger from History**: Enables retrigger using exact configuration from previous triggers
- **Multi-Workflow Support**: Supports CI, Pre-CD, Post-CD, and Deploy-CD workflows
- **Material Tracking**: Tracks git materials, commit information, and webhook data
- **Status Tracking**: Monitors trigger status (INITIATED, SUCCESS, FAILED)
- **REST API**: Provides APIs for viewing audit history and retrigger functionality

## Database Schema

### Tables Created

1. **workflow_trigger_audit**: Main audit table storing trigger metadata
2. **workflow_config_snapshot**: Stores complete workflow configuration snapshots
3. **trigger_material_snapshot**: Stores git material information for each trigger

### Key Fields

- **workflow_trigger_audit**: Links workflow ID to configuration snapshot
- **workflow_config_snapshot**: Stores JSON snapshots of all configurations
- **trigger_material_snapshot**: Stores git commit details and webhook data

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Trigger       │    │   Audit Hook     │    │   Audit Service │
│   Handlers      │───▶│                  │───▶│                 │
│   (CI/CD)       │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Integration    │    │   Repository    │
                       │   Layer          │    │   Layer         │
                       └──────────────────┘    └─────────────────┘
```

## Usage

### 1. Integration in Existing Trigger Handlers

#### CI Trigger Integration

```go
// In pkg/build/trigger/HandlerService.go
func (impl *HandlerServiceImpl) triggerCiPipeline(trigger types.Trigger) (int, error) {
    pipeline, variableSnapshot, savedCiWf, workflowRequest, err := impl.StartCiWorkflowAndPrepareWfRequest(trigger)
    if err != nil {
        return 0, err
    }
    
    // AUDIT: Start CI trigger audit
    err = impl.ciTriggerIntegration.AuditCiTriggerStart(trigger, pipeline, workflowRequest, savedCiWf.Id, nil)
    if err != nil {
        impl.Logger.Errorw("error in auditing CI trigger start", "err", err)
        // Continue with trigger even if audit fails
    }
    
    // ... existing trigger logic ...
    
    return savedCiWf.Id, err
}
```

#### Pre-CD Trigger Integration

```go
// In pkg/deployment/trigger/devtronApps/preStageHandlerCode.go
func (impl *HandlerServiceImpl) TriggerPreStage(request bean.TriggerRequest) (*bean6.ManifestPushTemplate, error) {
    // ... existing logic ...
    
    // AUDIT: Pre-CD trigger audit
    gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(request.Artifact)
    err = impl.triggerAuditHook.AuditPreCdTrigger(
        runner.Id,
        pipeline,
        env,
        cdStageWorkflowRequest,
        request.Artifact.Id,
        gitMaterials,
        "MANUAL", // or determine from request
        triggeredBy,
        triggerMetadata,
        nil,
    )
    
    // ... rest of existing logic ...
}
```

### 2. Retrigger Implementation

```go
// Retrigger from audit
retriggerConfig, err := auditService.GetWorkflowConfigForRetrigger(auditId)
if err != nil {
    return err
}

// Use historical workflow request instead of building new one
workflowId, err := retriggerIntegration.RetriggerFromAudit(auditId, triggeredBy)
```

### 3. REST API Usage

#### Get Trigger Audit History
```
GET /api/v1/workflow/trigger/audit/pipeline/{pipelineId}/history?workflowType=CI&limit=20&offset=0
```

#### Get Workflow Config Snapshot
```
GET /api/v1/workflow/trigger/audit/{auditId}/config
```

#### Retrigger from Audit
```
POST /api/v1/workflow/trigger/audit/{auditId}/retrigger
```

## Configuration

### Database Migration

Run the SQL migration files:
- `scripts/sql/320_workflow_trigger_audit.up.sql`

### Dependency Injection

Add the audit services to your dependency injection container:

```go
// Repository layer
workflowTriggerAuditRepository := repository.NewWorkflowTriggerAuditRepositoryImpl(db, logger, transactionUtil)
workflowConfigSnapshotRepository := repository.NewWorkflowConfigSnapshotRepositoryImpl(db, logger, transactionUtil)
triggerMaterialSnapshotRepository := repository.NewTriggerMaterialSnapshotRepositoryImpl(db, logger, transactionUtil)

// Service layer
workflowTriggerAuditService := service.NewWorkflowTriggerAuditServiceImpl(
    logger,
    workflowTriggerAuditRepository,
    workflowConfigSnapshotRepository,
    triggerMaterialSnapshotRepository,
    transactionUtil,
)

// Hook layer
triggerAuditHook := hook.NewTriggerAuditHookImpl(logger, workflowTriggerAuditService)

// Integration layer
ciTriggerIntegration := integration.NewCiTriggerIntegrationImpl(logger, triggerAuditHook, gitMaterialHelper)
retriggerIntegration := integration.NewRetriggerIntegrationImpl(logger, triggerAuditHook)

// REST handler
auditRestHandler := restHandler.NewWorkflowTriggerAuditRestHandlerImpl(
    logger,
    userService,
    validator,
    enforcer,
    workflowTriggerAuditService,
    retriggerIntegration,
)
```

## Data Flow

### Trigger Audit Flow

1. **Trigger Initiated**: Workflow trigger handler calls audit hook
2. **Configuration Capture**: All relevant configurations are captured and stored
3. **Material Extraction**: Git materials and commit information are extracted
4. **Audit Record Creation**: Audit record is created with references to snapshots
5. **Status Updates**: Audit status is updated based on workflow completion

### Retrigger Flow

1. **Audit Selection**: User selects audit record for retrigger
2. **Configuration Retrieval**: Historical configuration is retrieved from snapshots
3. **Workflow Recreation**: Workflow request is recreated from historical data
4. **Trigger Execution**: New workflow is triggered with historical configuration

## Benefits

1. **Complete Auditability**: Every trigger is fully audited with complete configuration
2. **Reliable Retrigger**: Retrigger uses exact historical configuration, not current config
3. **Debugging Support**: Historical configurations help debug deployment issues
4. **Compliance**: Provides audit trail for compliance requirements
5. **Rollback Capability**: Enables rollback to previous working configurations

## Performance Considerations

- **Async Auditing**: Audit operations don't block trigger execution
- **JSON Storage**: Configuration snapshots are stored as JSON for flexibility
- **Indexed Queries**: Database indexes optimize common query patterns
- **Configurable Retention**: Audit data can be archived/purged based on retention policies

## Future Enhancements

1. **Configuration Diff**: Compare configurations between triggers
2. **Bulk Retrigger**: Retrigger multiple workflows from audit
3. **Audit Analytics**: Analytics dashboard for trigger patterns
4. **Configuration Templates**: Create templates from successful configurations
5. **Automated Rollback**: Automatic rollback on failure detection
