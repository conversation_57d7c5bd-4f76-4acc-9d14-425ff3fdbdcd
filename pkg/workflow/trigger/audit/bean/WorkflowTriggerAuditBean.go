/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bean

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	repository4 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/repository"
	"time"
)

// CiTriggerAuditRequest represents the request for CI trigger audit
type CiTriggerAuditRequest struct {
	WorkflowId            int                        `json:"workflowId"`
	Pipeline              *pipelineConfig.CiPipeline `json:"pipeline"`
	WorkflowRequest       *types.WorkflowRequest     `json:"workflowRequest"`
	GitMaterials          []GitMaterial              `json:"gitMaterials"`
	TriggerType           string                     `json:"triggerType"` // MANUAL, AUTO, WEBHOOK
	TriggeredBy           int32                      `json:"triggeredBy"`
	TriggerMetadata       interface{}                `json:"triggerMetadata"`
	InfraConfigSnapshotId *int                       `json:"infraConfigSnapshotId"`
}

// CdTriggerAuditRequest represents the request for CD trigger audit (Pre-CD, Post-CD, Deploy-CD)
type CdTriggerAuditRequest struct {
	WorkflowRunnerId      int                      `json:"workflowRunnerId"`
	Pipeline              *pipelineConfig.Pipeline `json:"pipeline"`
	Environment           *repository4.Environment `json:"environment"`
	WorkflowRequest       *types.WorkflowRequest   `json:"workflowRequest"`
	ArtifactId            int                      `json:"artifactId"`
	GitMaterials          []GitMaterial            `json:"gitMaterials"`
	TriggerType           string                   `json:"triggerType"` // MANUAL, AUTO, WEBHOOK
	TriggeredBy           int32                    `json:"triggeredBy"`
	TriggerMetadata       interface{}              `json:"triggerMetadata"`
	InfraConfigSnapshotId *int                     `json:"infraConfigSnapshotId"`
}

// GitMaterial represents git material information for audit
type GitMaterial struct {
	GitMaterialId int         `json:"gitMaterialId"`
	CommitHash    string      `json:"commitHash"`
	CommitMessage string      `json:"commitMessage"`
	CommitAuthor  string      `json:"commitAuthor"`
	CommitDate    time.Time   `json:"commitDate"`
	Branch        string      `json:"branch"`
	Tag           string      `json:"tag"`
	WebhookData   interface{} `json:"webhookData,omitempty"`
}

// WorkflowTriggerAuditResponse represents the response for workflow trigger audit
type WorkflowTriggerAuditResponse struct {
	Id              int                                `json:"id"`
	WorkflowId      int                                `json:"workflowId"`
	WorkflowType    string                             `json:"workflowType"`
	PipelineId      int                                `json:"pipelineId"`
	AppId           int                                `json:"appId"`
	EnvironmentId   *int                               `json:"environmentId"`
	ArtifactId      *int                               `json:"artifactId"`
	TriggerType     string                             `json:"triggerType"`
	TriggeredBy     int32                              `json:"triggeredBy"`
	TriggerMetadata string                             `json:"triggerMetadata"`
	Status          string                             `json:"status"`
	CreatedOn       time.Time                          `json:"createdOn"`
	ConfigSnapshot  *repository.WorkflowConfigSnapshot `json:"configSnapshot"`
	GitMaterials    []GitMaterial                      `json:"gitMaterials"` // Parsed from JSON
}

// RetriggerWorkflowConfig represents the configuration needed for retrigger
type RetriggerWorkflowConfig struct {
	AuditId             int                                `json:"auditId"`
	WorkflowType        string                             `json:"workflowType"`
	PipelineId          int                                `json:"pipelineId"`
	AppId               int                                `json:"appId"`
	EnvironmentId       *int                               `json:"environmentId"`
	ArtifactId          *int                               `json:"artifactId"`
	WorkflowRequest     *types.WorkflowRequest             `json:"workflowRequest"`
	GitMaterials        []GitMaterial                      `json:"gitMaterials"`
	ConfigSnapshot      *repository.WorkflowConfigSnapshot `json:"configSnapshot"`
	OriginalTriggeredBy int32                              `json:"originalTriggeredBy"`
	OriginalTriggerTime time.Time                          `json:"originalTriggerTime"`
}

// TriggerAuditHistoryRequest represents the request for getting trigger audit history
type TriggerAuditHistoryRequest struct {
	PipelineId   int    `json:"pipelineId"`
	WorkflowType string `json:"workflowType"`
	Limit        int    `json:"limit"`
	Offset       int    `json:"offset"`
}

// TriggerAuditHistoryResponse represents the response for trigger audit history
type TriggerAuditHistoryResponse struct {
	TriggerAudits []*WorkflowTriggerAuditResponse `json:"triggerAudits"`
	TotalCount    int                             `json:"totalCount"`
}

// RetriggerRequest represents the request for retrigger
type RetriggerRequest struct {
	AuditId     int   `json:"auditId"`
	TriggeredBy int32 `json:"triggeredBy"`
}

// RetriggerResponse represents the response for retrigger
type RetriggerResponse struct {
	WorkflowId int    `json:"workflowId"`
	Status     string `json:"status"`
	Message    string `json:"message"`
}

// TriggerAuditStatusUpdateRequest represents the request for updating trigger audit status
type TriggerAuditStatusUpdateRequest struct {
	AuditId int    `json:"auditId"`
	Status  string `json:"status"`
}

// WorkflowConfigSnapshotResponse represents the response for workflow config snapshot
type WorkflowConfigSnapshotResponse struct {
	Id                    int       `json:"id"`
	WorkflowType          string    `json:"workflowType"`
	PipelineId            int       `json:"pipelineId"`
	AppId                 int       `json:"appId"`
	EnvironmentId         *int      `json:"environmentId"`
	WorkflowRequestJson   string    `json:"workflowRequestJson"`
	PipelineConfigJson    string    `json:"pipelineConfigJson"`
	DeploymentConfigJson  *string   `json:"deploymentConfigJson"`
	GitMaterialsJson      *string   `json:"gitMaterialsJson"`
	CiPipelineScriptsJson *string   `json:"ciPipelineScriptsJson"`
	PrePostScriptsJson    *string   `json:"prePostScriptsJson"`
	ConfigMapsJson        *string   `json:"configMapsJson"`
	SecretsJson           *string   `json:"secretsJson"`
	StrategyConfigJson    *string   `json:"strategyConfigJson"`
	CreatedOn             time.Time `json:"createdOn"`
}

// TriggerMaterialSnapshotResponse represents the response for trigger material snapshot
type TriggerMaterialSnapshotResponse struct {
	Id                     int        `json:"id"`
	WorkflowTriggerAuditId int        `json:"workflowTriggerAuditId"`
	GitMaterialId          *int       `json:"gitMaterialId"`
	CommitHash             *string    `json:"commitHash"`
	CommitMessage          *string    `json:"commitMessage"`
	CommitAuthor           *string    `json:"commitAuthor"`
	CommitDate             *time.Time `json:"commitDate"`
	Branch                 *string    `json:"branch"`
	Tag                    *string    `json:"tag"`
	WebhookData            *string    `json:"webhookData"`
	CreatedOn              time.Time  `json:"createdOn"`
}

// TriggerAuditSummary represents a summary of trigger audit for dashboard/overview
type TriggerAuditSummary struct {
	PipelineId         int       `json:"pipelineId"`
	PipelineName       string    `json:"pipelineName"`
	AppId              int       `json:"appId"`
	AppName            string    `json:"appName"`
	EnvironmentId      *int      `json:"environmentId"`
	EnvironmentName    *string   `json:"environmentName"`
	LastTriggeredOn    time.Time `json:"lastTriggeredOn"`
	LastTriggeredBy    int32     `json:"lastTriggeredBy"`
	TotalTriggers      int       `json:"totalTriggers"`
	SuccessfulTriggers int       `json:"successfulTriggers"`
	FailedTriggers     int       `json:"failedTriggers"`
}

// TriggerAuditFilter represents filters for trigger audit queries
type TriggerAuditFilter struct {
	AppIds         []int      `json:"appIds"`
	PipelineIds    []int      `json:"pipelineIds"`
	EnvironmentIds []int      `json:"environmentIds"`
	WorkflowTypes  []string   `json:"workflowTypes"`
	TriggerTypes   []string   `json:"triggerTypes"`
	TriggeredBy    []int32    `json:"triggeredBy"`
	FromDate       *time.Time `json:"fromDate"`
	ToDate         *time.Time `json:"toDate"`
	Status         []string   `json:"status"`
	Limit          int        `json:"limit"`
	Offset         int        `json:"offset"`
}
