/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package service

import (
	"encoding/json"
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/bean"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/repository"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"time"
)

type WorkflowTriggerAuditService interface {
	// SaveCiTriggerAudit saves audit data for CI trigger
	SaveCiTriggerAudit(request *bean.CiTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error)

	// SavePreCdTriggerAudit saves audit data for Pre-CD trigger
	SavePreCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error)

	// SavePostCdTriggerAudit saves audit data for Post-CD trigger
	SavePostCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error)

	// SaveDeployCdTriggerAudit saves audit data for Deploy-CD trigger
	SaveDeployCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error)

	// GetTriggerAuditByWorkflowId retrieves audit data by workflow ID and type
	GetTriggerAuditByWorkflowId(workflowId int, workflowType repository.WorkflowType) (*bean.WorkflowTriggerAuditResponse, error)

	// GetTriggerAuditHistory retrieves trigger audit history for a pipeline
	GetTriggerAuditHistory(pipelineId int, workflowType repository.WorkflowType, limit int, offset int) ([]*bean.WorkflowTriggerAuditResponse, error)

	// GetWorkflowConfigForRetrigger retrieves workflow configuration for retrigger
	GetWorkflowConfigForRetrigger(auditId int) (*bean.RetriggerWorkflowConfig, error)

	sql.TransactionWrapper
}

type WorkflowTriggerAuditServiceImpl struct {
	logger                            *zap.SugaredLogger
	workflowTriggerAuditRepository    repository.WorkflowTriggerAuditRepository
	workflowConfigSnapshotRepository  repository.WorkflowConfigSnapshotRepository
	triggerMaterialSnapshotRepository repository.TriggerMaterialSnapshotRepository
	*sql.TransactionUtilImpl
}

func NewWorkflowTriggerAuditServiceImpl(
	logger *zap.SugaredLogger,
	workflowTriggerAuditRepository repository.WorkflowTriggerAuditRepository,
	workflowConfigSnapshotRepository repository.WorkflowConfigSnapshotRepository,
	triggerMaterialSnapshotRepository repository.TriggerMaterialSnapshotRepository,
	transactionUtilImpl *sql.TransactionUtilImpl) *WorkflowTriggerAuditServiceImpl {

	return &WorkflowTriggerAuditServiceImpl{
		logger:                            logger,
		workflowTriggerAuditRepository:    workflowTriggerAuditRepository,
		workflowConfigSnapshotRepository:  workflowConfigSnapshotRepository,
		triggerMaterialSnapshotRepository: triggerMaterialSnapshotRepository,
		TransactionUtilImpl:               transactionUtilImpl,
	}
}

func (impl *WorkflowTriggerAuditServiceImpl) SaveCiTriggerAudit(request *bean.CiTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error) {
	tx, err := impl.StartTx()
	if err != nil {
		impl.logger.Errorw("error in starting transaction for CI trigger audit", "err", err)
		return nil, err
	}
	defer impl.RollbackTx(tx)

	// Save workflow config snapshot
	configSnapshot, err := impl.saveWorkflowConfigSnapshot(tx, request.WorkflowRequest, repository.CI_WORKFLOW_TYPE, request.Pipeline, nil)
	if err != nil {
		impl.logger.Errorw("error in saving workflow config snapshot for CI", "err", err)
		return nil, err
	}

	// Create trigger audit record
	triggerAudit := &repository.WorkflowTriggerAudit{
		WorkflowId:               request.WorkflowId,
		WorkflowType:             repository.CI_WORKFLOW_TYPE,
		PipelineId:               request.Pipeline.Id,
		AppId:                    request.Pipeline.AppId,
		EnvironmentId:            nil, // CI doesn't have environment
		ArtifactId:               nil, // CI creates artifact, doesn't consume
		TriggerType:              impl.getTriggerType(request.TriggerType),
		TriggeredBy:              request.TriggeredBy,
		TriggerMetadata:          impl.marshalTriggerMetadata(request.TriggerMetadata),
		WorkflowConfigSnapshotId: configSnapshot.Id,
		InfraConfigSnapshotId:    request.InfraConfigSnapshotId,
		Status:                   repository.AUDIT_INITIATED,
		AuditLog:                 sql.NewDefaultAuditLog(request.TriggeredBy),
	}

	savedAudit, err := impl.workflowTriggerAuditRepository.SaveWithTx(tx, triggerAudit)
	if err != nil {
		impl.logger.Errorw("error in saving CI trigger audit", "err", err)
		return nil, err
	}

	// Save material snapshots
	err = impl.saveMaterialSnapshots(tx, savedAudit.Id, request.GitMaterials, request.TriggeredBy)
	if err != nil {
		impl.logger.Errorw("error in saving material snapshots for CI", "err", err)
		return nil, err
	}

	err = impl.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing transaction for CI trigger audit", "err", err)
		return nil, err
	}

	return savedAudit, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) SavePreCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error) {
	return impl.saveCdTriggerAudit(request, repository.PRE_CD_WORKFLOW_TYPE)
}

func (impl *WorkflowTriggerAuditServiceImpl) SavePostCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error) {
	return impl.saveCdTriggerAudit(request, repository.POST_CD_WORKFLOW_TYPE)
}

func (impl *WorkflowTriggerAuditServiceImpl) SaveDeployCdTriggerAudit(request *bean.CdTriggerAuditRequest) (*repository.WorkflowTriggerAudit, error) {
	return impl.saveCdTriggerAudit(request, repository.DEPLOY_CD_WORKFLOW_TYPE)
}

func (impl *WorkflowTriggerAuditServiceImpl) saveCdTriggerAudit(request *bean.CdTriggerAuditRequest, workflowType repository.WorkflowType) (*repository.WorkflowTriggerAudit, error) {
	tx, err := impl.StartTx()
	if err != nil {
		impl.logger.Errorw("error in starting transaction for CD trigger audit", "err", err, "workflowType", workflowType)
		return nil, err
	}
	defer impl.RollbackTx(tx)

	// Save workflow config snapshot
	configSnapshot, err := impl.saveWorkflowConfigSnapshot(tx, request.WorkflowRequest, workflowType, request.Pipeline, request.Environment)
	if err != nil {
		impl.logger.Errorw("error in saving workflow config snapshot for CD", "err", err, "workflowType", workflowType)
		return nil, err
	}

	// Create trigger audit record
	triggerAudit := &repository.WorkflowTriggerAudit{
		WorkflowId:               request.WorkflowRunnerId,
		WorkflowType:             workflowType,
		PipelineId:               request.Pipeline.Id,
		AppId:                    request.Pipeline.AppId,
		EnvironmentId:            &request.Environment.Id,
		ArtifactId:               &request.ArtifactId,
		TriggerType:              impl.getTriggerType(request.TriggerType),
		TriggeredBy:              request.TriggeredBy,
		TriggerMetadata:          impl.marshalTriggerMetadata(request.TriggerMetadata),
		WorkflowConfigSnapshotId: configSnapshot.Id,
		InfraConfigSnapshotId:    request.InfraConfigSnapshotId,
		Status:                   repository.AUDIT_INITIATED,
		AuditLog:                 sql.NewDefaultAuditLog(request.TriggeredBy),
	}

	savedAudit, err := impl.workflowTriggerAuditRepository.SaveWithTx(tx, triggerAudit)
	if err != nil {
		impl.logger.Errorw("error in saving CD trigger audit", "err", err, "workflowType", workflowType)
		return nil, err
	}

	// Save material snapshots (from CI artifact)
	err = impl.saveMaterialSnapshots(tx, savedAudit.Id, request.GitMaterials, request.TriggeredBy)
	if err != nil {
		impl.logger.Errorw("error in saving material snapshots for CD", "err", err, "workflowType", workflowType)
		return nil, err
	}

	err = impl.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing transaction for CD trigger audit", "err", err, "workflowType", workflowType)
		return nil, err
	}

	return savedAudit, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) saveWorkflowConfigSnapshot(tx *pg.Tx, workflowRequest *types.WorkflowRequest, workflowType repository.WorkflowType, pipeline *pipelineConfig.Pipeline, environment interface{}) (*repository.WorkflowConfigSnapshot, error) {
	// Marshal workflow request
	workflowRequestJson, err := json.Marshal(workflowRequest)
	if err != nil {
		impl.logger.Errorw("error in marshaling workflow request", "err", err)
		return nil, err
	}

	// Marshal pipeline config
	pipelineConfigJson, err := json.Marshal(pipeline)
	if err != nil {
		impl.logger.Errorw("error in marshaling pipeline config", "err", err)
		return nil, err
	}

	configSnapshot := &repository.WorkflowConfigSnapshot{
		WorkflowType:        workflowType,
		PipelineId:          pipeline.Id,
		AppId:               pipeline.AppId,
		WorkflowRequestJson: string(workflowRequestJson),
		PipelineConfigJson:  string(pipelineConfigJson),
		AuditLog:            sql.NewDefaultAuditLog(workflowRequest.TriggeredBy),
	}

	// Set environment ID for CD workflows
	if environment != nil {
		// Handle different environment types based on workflow type
		// This would need to be adapted based on actual environment struct
		configSnapshot.EnvironmentId = &workflowRequest.EnvironmentId
	}

	// Save additional config based on workflow type
	err = impl.addAdditionalConfigToSnapshot(configSnapshot, workflowRequest, workflowType)
	if err != nil {
		impl.logger.Errorw("error in adding additional config to snapshot", "err", err)
		return nil, err
	}

	savedSnapshot, err := impl.workflowConfigSnapshotRepository.SaveWithTx(tx, configSnapshot)
	if err != nil {
		impl.logger.Errorw("error in saving workflow config snapshot", "err", err)
		return nil, err
	}

	return savedSnapshot, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) addAdditionalConfigToSnapshot(configSnapshot *repository.WorkflowConfigSnapshot, workflowRequest *types.WorkflowRequest, workflowType repository.WorkflowType) error {
	// Add deployment config for CD workflows
	if workflowType != repository.CI_WORKFLOW_TYPE && workflowRequest.Pipeline != nil {
		// This would need to fetch deployment config from the appropriate service
		// For now, we'll store basic deployment info
		deploymentConfig := map[string]interface{}{
			"environmentId": workflowRequest.EnvironmentId,
			"namespace":     workflowRequest.Namespace,
			"stageType":     workflowRequest.StageType,
		}
		deploymentConfigJson, err := json.Marshal(deploymentConfig)
		if err != nil {
			return err
		}
		deploymentConfigStr := string(deploymentConfigJson)
		configSnapshot.DeploymentConfigJson = &deploymentConfigStr
	}

	// Add CI scripts for CI workflows
	if workflowType == repository.CI_WORKFLOW_TYPE {
		ciScripts := map[string]interface{}{
			"preCiSteps":  workflowRequest.PreCiSteps,
			"postCiSteps": workflowRequest.PostCiSteps,
		}
		ciScriptsJson, err := json.Marshal(ciScripts)
		if err != nil {
			return err
		}
		ciScriptsStr := string(ciScriptsJson)
		configSnapshot.CiPipelineScriptsJson = &ciScriptsStr
	}

	// Add pre/post scripts for CD workflows
	if workflowType == repository.PRE_CD_WORKFLOW_TYPE || workflowType == repository.POST_CD_WORKFLOW_TYPE {
		prePostScripts := map[string]interface{}{
			"prePostDeploySteps": workflowRequest.PrePostDeploySteps,
		}
		prePostScriptsJson, err := json.Marshal(prePostScripts)
		if err != nil {
			return err
		}
		prePostScriptsStr := string(prePostScriptsJson)
		configSnapshot.PrePostScriptsJson = &prePostScriptsStr
	}

	return nil
}

func (impl *WorkflowTriggerAuditServiceImpl) saveMaterialSnapshots(tx *pg.Tx, auditId int, gitMaterials []bean.GitMaterial, triggeredBy int32) error {
	if len(gitMaterials) == 0 {
		return nil
	}

	var materialSnapshots []*repository.TriggerMaterialSnapshot
	for _, material := range gitMaterials {
		snapshot := &repository.TriggerMaterialSnapshot{
			WorkflowTriggerAuditId: auditId,
			GitMaterialId:          &material.GitMaterialId,
			CommitHash:             &material.CommitHash,
			CommitMessage:          &material.CommitMessage,
			CommitAuthor:           &material.CommitAuthor,
			CommitDate:             &material.CommitDate,
			Branch:                 &material.Branch,
			Tag:                    &material.Tag,
			AuditLog:               sql.NewDefaultAuditLog(triggeredBy),
		}

		if material.WebhookData != nil {
			webhookDataJson, err := json.Marshal(material.WebhookData)
			if err != nil {
				impl.logger.Errorw("error in marshaling webhook data", "err", err)
				return err
			}
			webhookDataStr := string(webhookDataJson)
			snapshot.WebhookData = &webhookDataStr
		}

		materialSnapshots = append(materialSnapshots, snapshot)
	}

	return impl.triggerMaterialSnapshotRepository.SaveBatchWithTx(tx, materialSnapshots)
}

func (impl *WorkflowTriggerAuditServiceImpl) getTriggerType(triggerType string) repository.TriggerType {
	switch triggerType {
	case "MANUAL":
		return repository.MANUAL_TRIGGER
	case "AUTO":
		return repository.AUTO_TRIGGER
	case "WEBHOOK":
		return repository.WEBHOOK_TRIGGER
	default:
		return repository.MANUAL_TRIGGER
	}
}

func (impl *WorkflowTriggerAuditServiceImpl) marshalTriggerMetadata(metadata interface{}) string {
	if metadata == nil {
		return "{}"
	}
	metadataJson, err := json.Marshal(metadata)
	if err != nil {
		impl.logger.Errorw("error in marshaling trigger metadata", "err", err)
		return "{}"
	}
	return string(metadataJson)
}

func (impl *WorkflowTriggerAuditServiceImpl) GetTriggerAuditByWorkflowId(workflowId int, workflowType repository.WorkflowType) (*bean.WorkflowTriggerAuditResponse, error) {
	audit, err := impl.workflowTriggerAuditRepository.FindByWorkflowIdAndType(workflowId, workflowType)
	if err != nil {
		impl.logger.Errorw("error in finding trigger audit by workflow id and type", "err", err, "workflowId", workflowId, "workflowType", workflowType)
		return nil, err
	}

	return impl.buildAuditResponse(audit)
}

func (impl *WorkflowTriggerAuditServiceImpl) GetTriggerAuditHistory(pipelineId int, workflowType repository.WorkflowType, limit int, offset int) ([]*bean.WorkflowTriggerAuditResponse, error) {
	audits, err := impl.workflowTriggerAuditRepository.FindByPipelineId(pipelineId, limit, offset)
	if err != nil {
		impl.logger.Errorw("error in finding trigger audit history", "err", err, "pipelineId", pipelineId)
		return nil, err
	}

	var responses []*bean.WorkflowTriggerAuditResponse
	for _, audit := range audits {
		if audit.WorkflowType == workflowType {
			response, err := impl.buildAuditResponse(audit)
			if err != nil {
				impl.logger.Errorw("error in building audit response", "err", err, "auditId", audit.Id)
				continue
			}
			responses = append(responses, response)
		}
	}

	return responses, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) buildAuditResponse(audit *repository.WorkflowTriggerAudit) (*bean.WorkflowTriggerAuditResponse, error) {
	// Get config snapshot
	configSnapshot, err := impl.workflowConfigSnapshotRepository.FindById(audit.WorkflowConfigSnapshotId)
	if err != nil {
		impl.logger.Errorw("error in finding config snapshot", "err", err, "snapshotId", audit.WorkflowConfigSnapshotId)
		return nil, err
	}

	// Get material snapshots
	materialSnapshots, err := impl.triggerMaterialSnapshotRepository.FindByWorkflowTriggerAuditId(audit.Id)
	if err != nil {
		impl.logger.Errorw("error in finding material snapshots", "err", err, "auditId", audit.Id)
		return nil, err
	}

	response := &bean.WorkflowTriggerAuditResponse{
		Id:                       audit.Id,
		WorkflowId:               audit.WorkflowId,
		WorkflowType:             string(audit.WorkflowType),
		PipelineId:               audit.PipelineId,
		AppId:                    audit.AppId,
		EnvironmentId:            audit.EnvironmentId,
		ArtifactId:               audit.ArtifactId,
		TriggerType:              string(audit.TriggerType),
		TriggeredBy:              audit.TriggeredBy,
		TriggerMetadata:          audit.TriggerMetadata,
		Status:                   string(audit.Status),
		CreatedOn:                audit.CreatedOn,
		ConfigSnapshot:           configSnapshot,
		MaterialSnapshots:        materialSnapshots,
	}

	return response, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) GetWorkflowConfigForRetrigger(auditId int) (*bean.RetriggerWorkflowConfig, error) {
	// Get trigger audit
	audit, err := impl.workflowTriggerAuditRepository.FindById(auditId)
	if err != nil {
		impl.logger.Errorw("error in finding trigger audit by id", "err", err, "auditId", auditId)
		return nil, err
	}

	// Get config snapshot
	configSnapshot, err := impl.workflowConfigSnapshotRepository.FindById(audit.WorkflowConfigSnapshotId)
	if err != nil {
		impl.logger.Errorw("error in finding config snapshot for retrigger", "err", err, "snapshotId", audit.WorkflowConfigSnapshotId)
		return nil, err
	}

	// Get material snapshots
	materialSnapshots, err := impl.triggerMaterialSnapshotRepository.FindByWorkflowTriggerAuditId(audit.Id)
	if err != nil {
		impl.logger.Errorw("error in finding material snapshots for retrigger", "err", err, "auditId", audit.Id)
		return nil, err
	}

	// Unmarshal workflow request
	var workflowRequest types.WorkflowRequest
	err = json.Unmarshal([]byte(configSnapshot.WorkflowRequestJson), &workflowRequest)
	if err != nil {
		impl.logger.Errorw("error in unmarshaling workflow request for retrigger", "err", err)
		return nil, err
	}

	// Build git materials from snapshots
	var gitMaterials []bean.GitMaterial
	for _, materialSnapshot := range materialSnapshots {
		material := bean.GitMaterial{
			GitMaterialId: *materialSnapshot.GitMaterialId,
			CommitHash:    *materialSnapshot.CommitHash,
			CommitMessage: *materialSnapshot.CommitMessage,
			CommitAuthor:  *materialSnapshot.CommitAuthor,
			CommitDate:    *materialSnapshot.CommitDate,
			Branch:        *materialSnapshot.Branch,
			Tag:           *materialSnapshot.Tag,
		}

		if materialSnapshot.WebhookData != nil {
			var webhookData interface{}
			err = json.Unmarshal([]byte(*materialSnapshot.WebhookData), &webhookData)
			if err != nil {
				impl.logger.Errorw("error in unmarshaling webhook data for retrigger", "err", err)
				return nil, err
			}
			material.WebhookData = webhookData
		}

		gitMaterials = append(gitMaterials, material)
	}

	retriggerConfig := &bean.RetriggerWorkflowConfig{
		AuditId:         audit.Id,
		WorkflowType:    string(audit.WorkflowType),
		PipelineId:      audit.PipelineId,
		AppId:           audit.AppId,
		EnvironmentId:   audit.EnvironmentId,
		ArtifactId:      audit.ArtifactId,
		WorkflowRequest: &workflowRequest,
		GitMaterials:    gitMaterials,
		ConfigSnapshot:  configSnapshot,
		OriginalTriggeredBy: audit.TriggeredBy,
		OriginalTriggerTime: audit.CreatedOn,
	}

	return retriggerConfig, nil
}

func (impl *WorkflowTriggerAuditServiceImpl) UpdateTriggerAuditStatus(auditId int, status repository.AuditStatus) error {
	audit, err := impl.workflowTriggerAuditRepository.FindById(auditId)
	if err != nil {
		impl.logger.Errorw("error in finding trigger audit for status update", "err", err, "auditId", auditId)
		return err
	}

	audit.Status = status
	audit.UpdatedOn = time.Now()

	err = impl.workflowTriggerAuditRepository.Update(audit)
	if err != nil {
		impl.logger.Errorw("error in updating trigger audit status", "err", err, "auditId", auditId, "status", status)
		return err
	}

	return nil
}


