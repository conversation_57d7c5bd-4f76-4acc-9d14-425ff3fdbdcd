# Workflow Trigger Audit - Integration Guide

This guide shows exactly where to add audit calls in the existing Devtron codebase.

## Simplified Approach

- **Single audit point**: Audit is saved only at trigger time, not during success/failure
- **Non-blocking**: Audit failures don't stop the trigger flow
- **Simple integration**: Just one method call per trigger type

## Integration Points

### 1. CI Trigger Integration

**File**: `pkg/build/trigger/HandlerService.go`
**Method**: `triggerCiPipeline`

```go
func (impl *HandlerServiceImpl) triggerCiPipeline(trigger types.Trigger) (int, error) {
    pipeline, variableSnapshot, savedCiWf, workflowRequest, err := impl.StartCiWorkflowAndPrepareWfRequest(trigger)
    if err != nil {
        return 0, err
    }
    
    // ADD THIS: CI trigger audit
    if impl.ciTriggerIntegration != nil {
        err = impl.ciTriggerIntegration.AuditCiTrigger(trigger, pipeline, workflowRequest, savedCiWf.Id, nil)
        if err != nil {
            impl.Logger.Errorw("error in auditing CI trigger", "err", err, "workflowId", savedCiWf.Id)
            // Continue with trigger even if audit fails
        }
    }
    
    // ... rest of existing code remains unchanged
    workflowRequest.CiPipelineType = trigger.PipelineType
    err = impl.executeCiPipeline(workflowRequest)
    // ... existing code continues
}
```

### 2. Pre-CD Trigger Integration

**File**: `pkg/deployment/trigger/devtronApps/preStageHandlerCode.go`
**Method**: `TriggerPreStage`

```go
func (impl *HandlerServiceImpl) TriggerPreStage(request bean.TriggerRequest) (*bean6.ManifestPushTemplate, error) {
    // ... existing code to create runner and workflow request ...
    
    // ADD THIS: Pre-CD trigger audit
    if impl.triggerAuditHook != nil {
        gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(request.Artifact)
        err = impl.triggerAuditHook.AuditPreCdTrigger(
            runner.Id,
            pipeline,
            env,
            cdStageWorkflowRequest,
            request.Artifact.Id,
            gitMaterials,
            "MANUAL", // or determine from request
            request.TriggeredBy,
            nil, // trigger metadata
            nil, // infra config snapshot id
        )
        if err != nil {
            impl.Logger.Errorw("error in auditing Pre-CD trigger", "err", err, "runnerId", runner.Id)
            // Continue with trigger even if audit fails
        }
    }
    
    // ... rest of existing code remains unchanged
}
```

### 3. Post-CD Trigger Integration

**File**: `pkg/deployment/trigger/devtronApps/postStageHandlerCode.go`
**Method**: `TriggerPostStage`

```go
func (impl *HandlerServiceImpl) TriggerPostStage(request bean.TriggerRequest) error {
    // ... existing code to create runner and workflow request ...
    
    // ADD THIS: Post-CD trigger audit
    if impl.triggerAuditHook != nil {
        gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(request.Artifact)
        err = impl.triggerAuditHook.AuditPostCdTrigger(
            runner.Id,
            pipeline,
            env,
            cdStageWorkflowRequest,
            request.Artifact.Id,
            gitMaterials,
            "MANUAL", // or determine from request
            request.TriggeredBy,
            nil, // trigger metadata
            nil, // infra config snapshot id
        )
        if err != nil {
            impl.Logger.Errorw("error in auditing Post-CD trigger", "err", err, "runnerId", runner.Id)
            // Continue with trigger even if audit fails
        }
    }
    
    // ... rest of existing code remains unchanged
}
```

### 4. Deploy-CD Trigger Integration

**File**: `pkg/deployment/trigger/devtronApps/deploymentHandlerCode.go`
**Method**: `TriggerDeployment`

```go
func (impl *HandlerServiceImpl) TriggerDeployment(request bean.TriggerRequest) (int, error) {
    // ... existing code to create runner and workflow request ...
    
    // ADD THIS: Deploy-CD trigger audit
    if impl.triggerAuditHook != nil {
        gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(request.Artifact)
        err = impl.triggerAuditHook.AuditDeployCdTrigger(
            runner.Id,
            pipeline,
            env,
            cdStageWorkflowRequest,
            request.Artifact.Id,
            gitMaterials,
            "MANUAL", // or determine from request
            request.TriggeredBy,
            nil, // trigger metadata
            nil, // infra config snapshot id
        )
        if err != nil {
            impl.Logger.Errorw("error in auditing Deploy-CD trigger", "err", err, "runnerId", runner.Id)
            // Continue with trigger even if audit fails
        }
    }
    
    // ... rest of existing code remains unchanged
}
```

## Dependency Injection

Add these to your wire.go or dependency injection setup:

```go
// Repository layer
workflowTriggerAuditRepository := repository.NewWorkflowTriggerAuditRepositoryImpl(db, logger, transactionUtil)
workflowConfigSnapshotRepository := repository.NewWorkflowConfigSnapshotRepositoryImpl(db, logger, transactionUtil)
triggerMaterialSnapshotRepository := repository.NewTriggerMaterialSnapshotRepositoryImpl(db, logger, transactionUtil)

// Service layer
workflowTriggerAuditService := service.NewWorkflowTriggerAuditServiceImpl(
    logger,
    workflowTriggerAuditRepository,
    workflowConfigSnapshotRepository,
    triggerMaterialSnapshotRepository,
    transactionUtil,
)

// Helper layer
gitMaterialHelper := helper.NewGitMaterialHelperImpl(logger)

// Hook layer
triggerAuditHook := hook.NewTriggerAuditHookImpl(logger, workflowTriggerAuditService)

// Integration layer
ciTriggerIntegration := integration.NewCiTriggerIntegrationImpl(logger, triggerAuditHook, gitMaterialHelper)
retriggerIntegration := integration.NewRetriggerIntegrationImpl(logger, triggerAuditHook)
```

## Handler Service Updates

Update your handler service constructors to include the audit dependencies:

```go
// CI Handler Service
type HandlerServiceImpl struct {
    // ... existing fields
    ciTriggerIntegration integration.CiTriggerIntegration
}

// CD Handler Service
type HandlerServiceImpl struct {
    // ... existing fields
    triggerAuditHook hook.TriggerAuditHook
    gitMaterialHelper helper.GitMaterialHelper
}
```

## Benefits of This Approach

1. **Simple**: Only one audit call per trigger type
2. **Non-intrusive**: Existing trigger flow remains unchanged
3. **Fail-safe**: Audit failures don't break triggers
4. **Complete**: Captures all necessary configuration data
5. **Retrigger-ready**: Enables retrigger from historical snapshots

## What Gets Audited

For each trigger, the system captures:
- Complete `WorkflowRequest` JSON (as sent to ci-runner)
- Pipeline configuration at trigger time
- Git materials with commit details
- Environment configuration (for CD workflows)
- Infrastructure configuration (linked to existing table)
- Trigger metadata (manual/auto/webhook, user, timestamp)

## Retrigger Usage

Once auditing is in place, retrigger can be implemented:

```go
// Get historical config
config, err := triggerAuditHook.GetRetriggerConfig(auditId)

// Use historical WorkflowRequest instead of building new one
workflowId, err := retriggerIntegration.RetriggerFromAudit(auditId, triggeredBy)
```

This approach keeps the complexity low while providing complete audit functionality and enabling reliable retrigger from historical configurations.
