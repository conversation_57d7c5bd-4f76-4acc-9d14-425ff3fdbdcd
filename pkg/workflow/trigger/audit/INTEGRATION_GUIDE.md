# Workflow Trigger Audit - Integration Guide

This guide shows exactly where to add audit calls in the existing Devtron codebase.

## Simplified Approach

- **Single audit point**: Audit is saved only at trigger time, not during success/failure
- **Single table**: Consolidated `workflow_config_snapshot` table instead of separate audit and config tables
- **Non-blocking**: Audit failures don't stop the trigger flow
- **Simple integration**: Just one method call per trigger type

## Integration Points

### 1. CI Trigger Integration

**File**: `pkg/build/trigger/HandlerService.go`
**Method**: `triggerCiPipeline`

```go
func (impl *HandlerServiceImpl) triggerCiPipeline(trigger types.Trigger) (int, error) {
    pipeline, variableSnapshot, savedCiWf, workflowRequest, err := impl.StartCiWorkflowAndPrepareWfRequest(trigger)
    if err != nil {
        return 0, err
    }

    // ADD THIS: CI trigger audit
    if impl.workflowTriggerIntegration != nil {
        err = impl.workflowTriggerIntegration.AuditCiTrigger(trigger, pipeline, workflowRequest, savedCiWf.Id, nil)
        if err != nil {
            impl.Logger.Errorw("error in auditing CI trigger", "err", err, "workflowId", savedCiWf.Id)
            // Continue with trigger even if audit fails
        }
    }

    // ... rest of existing code remains unchanged
    workflowRequest.CiPipelineType = trigger.PipelineType
    err = impl.executeCiPipeline(workflowRequest)
    // ... existing code continues
}
```

### 2. Pre-CD Trigger Integration

**File**: `pkg/deployment/trigger/devtronApps/preStageHandlerCode.go`
**Method**: `TriggerPreStage` (line 64)

Add audit call after line 132 (after `cdStageWorkflowRequest.StageType = types.PRE`):

```go
func (impl *HandlerServiceImpl) TriggerPreStage(request bean.TriggerRequest) (*bean6.ManifestPushTemplate, error) {
    // ... existing code until line 132 ...

    cdStageWorkflowRequest.StageType = types.PRE

    // ADD THIS: Pre-CD trigger audit
    if impl.workflowTriggerIntegration != nil {
        gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(artifact)
        err = impl.workflowTriggerIntegration.AuditPreCdTrigger(
            runner.Id,
            pipeline,
            env,
            cdStageWorkflowRequest,
            artifact.Id,
            gitMaterials,
            helper.DetermineTriggerTypeFromCdContext(request.TriggerContext.TriggerType),
            triggeredBy,
            nil, // trigger metadata
            nil, // infra config snapshot id
        )
        if err != nil {
            impl.logger.Errorw("error in auditing Pre-CD trigger", "err", err, "runnerId", runner.Id)
            // Continue with trigger even if audit fails
        }
    }

    // handling copyContainerImage plugin specific logic
    imagePathReservationIds, err := impl.setCopyContainerImagePluginDataAndReserveImages(cdStageWorkflowRequest, pipeline.Id, types.PRE, artifact)
    // ... rest of existing code remains unchanged
}
```

### 3. Post-CD Trigger Integration

**File**: `pkg/deployment/trigger/devtronApps/postStageHandlerCode.go`
**Method**: `TriggerPostStage` (line 33)

Add audit call after line 114 (after `cdStageWorkflowRequest.Type = bean3.CD_WORKFLOW_PIPELINE_TYPE`):

```go
func (impl *HandlerServiceImpl) TriggerPostStage(request bean.TriggerRequest) (*bean4.ManifestPushTemplate, error) {
    // ... existing code until line 114 ...

    cdStageWorkflowRequest.StageType = types.POST
    cdStageWorkflowRequest.Pipeline = pipeline
    cdStageWorkflowRequest.Env = env
    cdStageWorkflowRequest.Type = bean3.CD_WORKFLOW_PIPELINE_TYPE

    // ADD THIS: Post-CD trigger audit
    if impl.workflowTriggerIntegration != nil {
        gitMaterials, _ := impl.gitMaterialHelper.ExtractGitMaterialsFromCiArtifact(cdWf.CiArtifact)
        err = impl.workflowTriggerIntegration.AuditPostCdTrigger(
            runner.Id,
            pipeline,
            env,
            cdStageWorkflowRequest,
            cdWf.CiArtifact.Id,
            gitMaterials,
            helper.DetermineTriggerTypeFromCdContext(request.TriggerContext.TriggerType),
            triggeredBy,
            nil, // trigger metadata
            nil, // infra config snapshot id
        )
        if err != nil {
            impl.logger.Errorw("error in auditing Post-CD trigger", "err", err, "runnerId", runner.Id)
            // Continue with trigger even if audit fails
        }
    }

    // handling plugin specific logic
    pluginImagePathReservationIds, err := impl.setCopyContainerImagePluginDataAndReserveImages(cdStageWorkflowRequest, pipeline.Id, types.POST, cdWf.CiArtifact)
    // ... rest of existing code remains unchanged
}
```



## Dependency Injection

Add these to your wire.go or dependency injection setup:

```go
// Repository layer (consolidated single table approach)
workflowConfigSnapshotRepository := repository.NewWorkflowConfigSnapshotRepositoryImpl(db, logger, transactionUtil)
triggerMaterialSnapshotRepository := repository.NewTriggerMaterialSnapshotRepositoryImpl(db, logger, transactionUtil)

// Service layer
workflowTriggerAuditService := service.NewWorkflowTriggerAuditServiceImpl(
    logger,
    workflowConfigSnapshotRepository,
    triggerMaterialSnapshotRepository,
    transactionUtil,
)

// Helper layer
gitMaterialHelper := helper.NewGitMaterialHelperImpl(logger)

// Hook layer
triggerAuditHook := hook.NewTriggerAuditHookImpl(logger, workflowTriggerAuditService)

// Integration layer
workflowTriggerIntegration := integration.NewWorkflowTriggerIntegrationImpl(logger, triggerAuditHook, gitMaterialHelper)
retriggerIntegration := integration.NewRetriggerIntegrationImpl(logger, triggerAuditHook)
```

## Handler Service Updates

Update your handler service constructors to include the audit dependencies:

```go
// CI Handler Service
type HandlerServiceImpl struct {
    // ... existing fields
    workflowTriggerIntegration integration.WorkflowTriggerIntegration
}

// CD Handler Service
type HandlerServiceImpl struct {
    // ... existing fields
    workflowTriggerIntegration integration.WorkflowTriggerIntegration
    gitMaterialHelper helper.GitMaterialHelper
}
```

## Benefits of This Approach

1. **Simple**: Only one audit call per trigger type
2. **Non-intrusive**: Existing trigger flow remains unchanged
3. **Fail-safe**: Audit failures don't break triggers
4. **Complete**: Captures all necessary configuration data
5. **Retrigger-ready**: Enables retrigger from historical snapshots

## What Gets Audited

For each trigger, the system captures:
- Complete `WorkflowRequest` JSON (as sent to ci-runner)
- Pipeline configuration at trigger time
- Git materials with commit details
- Environment configuration (for CD workflows)
- Infrastructure configuration (linked to existing table)
- Trigger metadata (manual/auto/webhook, user, timestamp)

## Retrigger Usage

Once auditing is in place, retrigger can be implemented:

```go
// Get historical config
config, err := triggerAuditHook.GetRetriggerConfig(auditId)

// Use historical WorkflowRequest instead of building new one
workflowId, err := retriggerIntegration.RetriggerFromAudit(auditId, triggeredBy)
```

This approach keeps the complexity low while providing complete audit functionality and enabling reliable retrigger from historical configurations.
