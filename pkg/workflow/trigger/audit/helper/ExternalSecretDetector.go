/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"go.uber.org/zap"
)

// ExternalSecretDetector integrates with professional secret detection services
type ExternalSecretDetector interface {
	// DetectSecrets scans content for secrets using external service
	DetectSecrets(content string) ([]SecretDetection, error)
	
	// IsSecret checks if a specific value is a secret
	IsSecret(fieldName string, value string) (bool, error)
	
	// GetSupportedSecretTypes returns list of secret types the detector can find
	GetSupportedSecretTypes() []string
}

// SecretDetection represents a detected secret
type SecretDetection struct {
	Type        string  `json:"type"`
	Value       string  `json:"value"`
	Confidence  float64 `json:"confidence"`
	StartPos    int     `json:"start_pos"`
	EndPos      int     `json:"end_pos"`
	Description string  `json:"description"`
}

// GitLeaksDetector uses GitLeaks library for secret detection
type GitLeaksDetector struct {
	logger  *zap.SugaredLogger
	timeout time.Duration
}

func NewGitLeaksDetector(logger *zap.SugaredLogger) *GitLeaksDetector {
	return &GitLeaksDetector{
		logger:  logger,
		timeout: 5 * time.Second,
	}
}

func (impl *GitLeaksDetector) DetectSecrets(content string) ([]SecretDetection, error) {
	// This would integrate with actual GitLeaks library
	// For now, showing the interface structure
	
	ctx, cancel := context.WithTimeout(context.Background(), impl.timeout)
	defer cancel()
	
	// Example integration (pseudo-code):
	/*
	import "github.com/zricethezav/gitleaks/v8/detect"
	
	detector := detect.NewDetector(detect.Config{
		Rules: detect.DefaultRules(),
	})
	
	findings, err := detector.DetectString(ctx, content)
	if err != nil {
		return nil, err
	}
	
	var detections []SecretDetection
	for _, finding := range findings {
		detection := SecretDetection{
			Type:        finding.RuleID,
			Value:       finding.Secret,
			Confidence:  1.0, // GitLeaks doesn't provide confidence scores
			StartPos:    finding.StartLine,
			EndPos:      finding.EndLine,
			Description: finding.Description,
		}
		detections = append(detections, detection)
	}
	
	return detections, nil
	*/
	
	// Mock implementation for demonstration
	return impl.mockDetectSecrets(content), nil
}

func (impl *GitLeaksDetector) IsSecret(fieldName string, value string) (bool, error) {
	detections, err := impl.DetectSecrets(fmt.Sprintf("%s: %s", fieldName, value))
	if err != nil {
		return false, err
	}
	
	return len(detections) > 0, nil
}

func (impl *GitLeaksDetector) GetSupportedSecretTypes() []string {
	return []string{
		"aws-access-token",
		"aws-secret-key",
		"github-pat",
		"github-oauth",
		"slack-bot-token",
		"slack-user-token",
		"openai-api-key",
		"stripe-api-key",
		"jwt-token",
		"private-key",
		"certificate",
		"database-url",
		"generic-api-key",
	}
}

func (impl *GitLeaksDetector) mockDetectSecrets(content string) []SecretDetection {
	var detections []SecretDetection
	
	// Mock detection logic for demonstration
	if strings.Contains(content, "sk-") {
		detections = append(detections, SecretDetection{
			Type:        "openai-api-key",
			Value:       "sk-***",
			Confidence:  0.95,
			Description: "OpenAI API Key",
		})
	}
	
	if strings.Contains(content, "ghp_") {
		detections = append(detections, SecretDetection{
			Type:        "github-pat",
			Value:       "ghp_***",
			Confidence:  0.98,
			Description: "GitHub Personal Access Token",
		})
	}
	
	if strings.Contains(content, "AKIA") {
		detections = append(detections, SecretDetection{
			Type:        "aws-access-token",
			Value:       "AKIA***",
			Confidence:  0.95,
			Description: "AWS Access Key ID",
		})
	}
	
	return detections
}

// TruffleHogDetector uses TruffleHog for secret detection
type TruffleHogDetector struct {
	logger  *zap.SugaredLogger
	timeout time.Duration
}

func NewTruffleHogDetector(logger *zap.SugaredLogger) *TruffleHogDetector {
	return &TruffleHogDetector{
		logger:  logger,
		timeout: 5 * time.Second,
	}
}

func (impl *TruffleHogDetector) DetectSecrets(content string) ([]SecretDetection, error) {
	// This would integrate with actual TruffleHog library
	// Example integration (pseudo-code):
	/*
	import "github.com/trufflesecurity/trufflehog/v3/pkg/engine"
	
	e := engine.Start(ctx)
	defer e.Finish(ctx)
	
	// Scan the content
	e.ScanReader(ctx, strings.NewReader(content))
	
	// Get results
	results := e.GetResults()
	
	var detections []SecretDetection
	for result := range results {
		detection := SecretDetection{
			Type:        result.DetectorType.String(),
			Value:       string(result.Raw),
			Confidence:  float64(result.Verified), // 0 or 1
			Description: result.DetectorType.String(),
		}
		detections = append(detections, detection)
	}
	
	return detections, nil
	*/
	
	return impl.mockDetectSecrets(content), nil
}

func (impl *TruffleHogDetector) IsSecret(fieldName string, value string) (bool, error) {
	detections, err := impl.DetectSecrets(value)
	return len(detections) > 0, err
}

func (impl *TruffleHogDetector) GetSupportedSecretTypes() []string {
	return []string{
		"AWS",
		"GitHub",
		"Slack",
		"OpenAI",
		"Stripe",
		"JWT",
		"PrivateKey",
		"Database",
		"Generic",
	}
}

func (impl *TruffleHogDetector) mockDetectSecrets(content string) []SecretDetection {
	// Similar mock implementation
	return []SecretDetection{}
}

// CompositeSecretDetector combines multiple detection methods
type CompositeSecretDetector struct {
	logger             *zap.SugaredLogger
	detectors          []ExternalSecretDetector
	advancedDetector   AdvancedSecretDetector
	confidenceThreshold float64
}

func NewCompositeSecretDetector(logger *zap.SugaredLogger) *CompositeSecretDetector {
	return &CompositeSecretDetector{
		logger: logger,
		detectors: []ExternalSecretDetector{
			NewGitLeaksDetector(logger),
			NewTruffleHogDetector(logger),
		},
		advancedDetector:    NewAdvancedSecretDetectorImpl(logger),
		confidenceThreshold: 0.7,
	}
}

func (impl *CompositeSecretDetector) IsSecret(fieldName string, value string) bool {
	// 1. Try external detectors first (most accurate)
	for _, detector := range impl.detectors {
		isSecret, err := detector.IsSecret(fieldName, value)
		if err != nil {
			impl.logger.Warnw("external detector failed", "err", err)
			continue
		}
		if isSecret {
			impl.logger.Debugw("secret detected by external detector", "field", fieldName)
			return true
		}
	}
	
	// 2. Fallback to advanced internal detector
	confidence := impl.advancedDetector.GetSecretConfidence(fieldName, value)
	isSecret := float64(confidence)/100.0 >= impl.confidenceThreshold
	
	if isSecret {
		impl.logger.Debugw("secret detected by advanced detector", 
			"field", fieldName, 
			"confidence", confidence)
	}
	
	return isSecret
}

func (impl *CompositeSecretDetector) DetectAllSecrets(content string) ([]SecretDetection, error) {
	var allDetections []SecretDetection
	
	// Collect detections from all external detectors
	for _, detector := range impl.detectors {
		detections, err := detector.DetectSecrets(content)
		if err != nil {
			impl.logger.Warnw("detector failed", "err", err)
			continue
		}
		allDetections = append(allDetections, detections...)
	}
	
	// Deduplicate detections
	return impl.deduplicateDetections(allDetections), nil
}

func (impl *CompositeSecretDetector) deduplicateDetections(detections []SecretDetection) []SecretDetection {
	seen := make(map[string]bool)
	var unique []SecretDetection
	
	for _, detection := range detections {
		key := fmt.Sprintf("%s:%s", detection.Type, detection.Value)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, detection)
		}
	}
	
	return unique
}

/*
Integration Example:

// In your SecretSanitizer, replace the simple pattern matching:

func (impl *SecretSanitizerImpl) isRuntimeParameterSecret(fieldPath string, value string) bool {
    // Check if this is within runtime parameters
    if !strings.Contains(fieldPath, "runtimeParameters") {
        return false
    }
    
    // Use composite detector instead of simple patterns
    detector := NewCompositeSecretDetector(impl.logger)
    return detector.IsSecret(fieldPath, value)
}

Benefits of External Libraries:

1. **Professional Quality**: Battle-tested detection rules
2. **Regular Updates**: New secret patterns added automatically  
3. **High Accuracy**: Lower false positive rates
4. **Comprehensive Coverage**: Hundreds of secret types
5. **Performance**: Optimized for speed
6. **Verification**: Some can verify if secrets are valid
7. **Community Support**: Maintained by security experts

Recommended Libraries:

1. **GitLeaks** (github.com/zricethezav/gitleaks/v8)
   - Pros: Fast, comprehensive rules, actively maintained
   - Cons: No confidence scoring
   - Best for: General purpose secret detection

2. **TruffleHog** (github.com/trufflesecurity/trufflehog/v3)
   - Pros: Secret verification, high accuracy
   - Cons: Slower, more complex
   - Best for: High-security environments

3. **detect-secrets** (Python, can be called via exec)
   - Pros: Very mature, extensive rules
   - Cons: Python dependency
   - Best for: Maximum detection coverage

Implementation Strategy:

1. Start with GitLeaks for runtime parameter detection
2. Keep field-specific detection for known fields
3. Use composite approach for best coverage
4. Add confidence thresholds for tuning
5. Monitor false positive rates
*/
