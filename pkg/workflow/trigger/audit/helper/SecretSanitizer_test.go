/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"encoding/json"
	"testing"
	"go.uber.org/zap"
)

func TestSecretSanitizer_FieldSpecific(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	sanitizer := NewSecretSanitizerImpl(logger.Sugar())

	// Test data with the exact fields you mentioned
	testWorkflowRequest := map[string]interface{}{
		"dockerRegistry": map[string]interface{}{
			"dockerPassword": "super-secret-password",
			"username":       "myuser",
			"url":           "docker.io",
		},
		"blobStorageS3Config": map[string]interface{}{
			"accessKey":    "AKIAIOSFODNN7EXAMPLE",
			"secretKey":    "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
			"passkey":      "another-secret",
			"bucketName":   "my-bucket",
			"region":       "us-east-1",
		},
		"azureBlobConfig": map[string]interface{}{
			"accountKey":   "base64-encoded-account-key",
			"accountName":  "mystorageaccount",
			"containerName": "mycontainer",
		},
		"gcpBlobConfig": map[string]interface{}{
			"credentialFileData": `{
				"type": "service_account",
				"project_id": "my-project",
				"private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC..."
			}`,
			"bucketName": "my-gcp-bucket",
		},
		"dockerCert": "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV...",
		"runtimeParameters": map[string]interface{}{
			"API_KEY":           "sk-1234567890abcdef1234567890abcdef",
			"DATABASE_PASSWORD": "my-db-password-123",
			"NORMAL_PARAM":      "just-a-normal-value",
			"SHORT_VALUE":       "abc",
		},
		"normalField": "this-should-not-be-sanitized",
		"appName":     "my-app",
	}

	// Sanitize the workflow request
	sanitized, err := sanitizer.SanitizeWorkflowRequest(testWorkflowRequest)
	if err != nil {
		t.Fatalf("Error sanitizing workflow request: %v", err)
	}

	// Convert to map for easier testing
	sanitizedMap := sanitized.(map[string]interface{})

	// Test 1: dockerPassword should be sanitized
	dockerRegistry := sanitizedMap["dockerRegistry"].(map[string]interface{})
	if dockerRegistry["dockerPassword"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("dockerPassword was not sanitized. Got: %v", dockerRegistry["dockerPassword"])
	}
	if dockerRegistry["username"] != "myuser" {
		t.Errorf("username should not be sanitized. Got: %v", dockerRegistry["username"])
	}

	// Test 2: blobStorageS3Config secrets should be sanitized
	blobS3Config := sanitizedMap["blobStorageS3Config"].(map[string]interface{})
	if blobS3Config["accessKey"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("blobStorageS3Config.accessKey was not sanitized. Got: %v", blobS3Config["accessKey"])
	}
	if blobS3Config["secretKey"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("blobStorageS3Config.secretKey was not sanitized. Got: %v", blobS3Config["secretKey"])
	}
	if blobS3Config["passkey"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("blobStorageS3Config.passkey was not sanitized. Got: %v", blobS3Config["passkey"])
	}
	if blobS3Config["bucketName"] != "my-bucket" {
		t.Errorf("bucketName should not be sanitized. Got: %v", blobS3Config["bucketName"])
	}

	// Test 3: azureBlobConfig.accountKey should be sanitized
	azureBlobConfig := sanitizedMap["azureBlobConfig"].(map[string]interface{})
	if azureBlobConfig["accountKey"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("azureBlobConfig.accountKey was not sanitized. Got: %v", azureBlobConfig["accountKey"])
	}
	if azureBlobConfig["accountName"] != "mystorageaccount" {
		t.Errorf("accountName should not be sanitized. Got: %v", azureBlobConfig["accountName"])
	}

	// Test 4: gcpBlobConfig.credentialFileData should be sanitized
	gcpBlobConfig := sanitizedMap["gcpBlobConfig"].(map[string]interface{})
	if gcpBlobConfig["credentialFileData"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("gcpBlobConfig.credentialFileData was not sanitized. Got: %v", gcpBlobConfig["credentialFileData"])
	}

	// Test 5: dockerCert should be sanitized
	if sanitizedMap["dockerCert"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("dockerCert was not sanitized. Got: %v", sanitizedMap["dockerCert"])
	}

	// Test 6: Runtime parameters with secrets should be sanitized
	runtimeParams := sanitizedMap["runtimeParameters"].(map[string]interface{})
	if runtimeParams["API_KEY"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("Runtime parameter API_KEY was not sanitized. Got: %v", runtimeParams["API_KEY"])
	}
	if runtimeParams["DATABASE_PASSWORD"] != SANITIZED_SECRET_PLACEHOLDER {
		t.Errorf("Runtime parameter DATABASE_PASSWORD was not sanitized. Got: %v", runtimeParams["DATABASE_PASSWORD"])
	}
	if runtimeParams["NORMAL_PARAM"] != "just-a-normal-value" {
		t.Errorf("Normal runtime parameter should not be sanitized. Got: %v", runtimeParams["NORMAL_PARAM"])
	}
	if runtimeParams["SHORT_VALUE"] != "abc" {
		t.Errorf("Short runtime parameter should not be sanitized. Got: %v", runtimeParams["SHORT_VALUE"])
	}

	// Test 7: Normal fields should not be sanitized
	if sanitizedMap["normalField"] != "this-should-not-be-sanitized" {
		t.Errorf("Normal field was sanitized. Got: %v", sanitizedMap["normalField"])
	}
	if sanitizedMap["appName"] != "my-app" {
		t.Errorf("App name was sanitized. Got: %v", sanitizedMap["appName"])
	}

	// Print sanitized result for visual inspection
	sanitizedJson, _ := json.MarshalIndent(sanitized, "", "  ")
	t.Logf("Sanitized workflow request:\n%s", string(sanitizedJson))
}

func TestSecretSanitizer_Reconstruction(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	sanitizer := NewSecretSanitizerImpl(logger.Sugar())

	// Test data with sanitized secrets
	sanitizedWorkflowRequest := map[string]interface{}{
		"dockerRegistry": map[string]interface{}{
			"dockerPassword": SANITIZED_SECRET_PLACEHOLDER,
			"username":       "myuser",
		},
		"blobStorageS3Config": map[string]interface{}{
			"accessKey":  SANITIZED_SECRET_PLACEHOLDER,
			"secretKey":  SANITIZED_SECRET_PLACEHOLDER,
			"bucketName": "my-bucket",
		},
		"normalField": "this-should-remain-unchanged",
	}

	// Reconstruct secrets
	reconstructed, err := sanitizer.ReconstructSecrets(sanitizedWorkflowRequest, nil, 123)
	if err != nil {
		t.Fatalf("Error reconstructing secrets: %v", err)
	}

	// Convert to map for testing
	reconstructedMap := reconstructed.(map[string]interface{})

	// Test 1: Sanitized secrets should be replaced with empty strings
	dockerRegistry := reconstructedMap["dockerRegistry"].(map[string]interface{})
	if dockerRegistry["dockerPassword"] != "" {
		t.Errorf("Sanitized dockerPassword should be empty string. Got: %v", dockerRegistry["dockerPassword"])
	}
	if dockerRegistry["username"] != "myuser" {
		t.Errorf("Non-secret field should remain unchanged. Got: %v", dockerRegistry["username"])
	}

	// Test 2: Nested sanitized secrets should be replaced
	blobS3Config := reconstructedMap["blobStorageS3Config"].(map[string]interface{})
	if blobS3Config["accessKey"] != "" {
		t.Errorf("Sanitized accessKey should be empty string. Got: %v", blobS3Config["accessKey"])
	}
	if blobS3Config["secretKey"] != "" {
		t.Errorf("Sanitized secretKey should be empty string. Got: %v", blobS3Config["secretKey"])
	}
	if blobS3Config["bucketName"] != "my-bucket" {
		t.Errorf("Non-secret field should remain unchanged. Got: %v", blobS3Config["bucketName"])
	}

	// Test 3: Normal fields should remain unchanged
	if reconstructedMap["normalField"] != "this-should-remain-unchanged" {
		t.Errorf("Normal field should remain unchanged. Got: %v", reconstructedMap["normalField"])
	}

	// Print reconstructed result for visual inspection
	reconstructedJson, _ := json.MarshalIndent(reconstructed, "", "  ")
	t.Logf("Reconstructed workflow request:\n%s", string(reconstructedJson))
}

func TestSecretSanitizer_RuntimeParameterDetection(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	sanitizer := NewSecretSanitizerImpl(logger.Sugar())

	testCases := []struct {
		name     string
		value    string
		expected bool
	}{
		{"API Key", "sk-1234567890abcdef1234567890abcdef", true},
		{"GitHub Token", "ghp_1234567890abcdef1234567890abcdef123456", true},
		{"AWS Access Key", "AKIAIOSFODNN7EXAMPLE", true},
		{"Bearer Token", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", true},
		{"Certificate", "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAKoK...", true},
		{"Long Base64", "dGhpc2lzYXZlcnlsb25nYmFzZTY0ZW5jb2RlZHN0cmluZ3RoYXRsb29rc2xpa2Vhc2VjcmV0", true},
		{"Normal Value", "just-a-normal-value", false},
		{"Short Value", "abc", false},
		{"Number", "12345", false},
		{"URL", "https://example.com/api/v1", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := sanitizer.isValueLikelySecret(tc.value)
			if result != tc.expected {
				t.Errorf("isValueLikelySecret(%q) = %v, expected %v", tc.value, result, tc.expected)
			}
		})
	}
}

/*
Expected Test Results:

✅ dockerPassword → ***SANITIZED***
✅ blobStorageS3Config.accessKey → ***SANITIZED***
✅ blobStorageS3Config.secretKey → ***SANITIZED***
✅ blobStorageS3Config.passkey → ***SANITIZED***
✅ azureBlobConfig.accountKey → ***SANITIZED***
✅ gcpBlobConfig.credentialFileData → ***SANITIZED***
✅ dockerCert → ***SANITIZED***
✅ Runtime parameter API_KEY → ***SANITIZED***
✅ Runtime parameter DATABASE_PASSWORD → ***SANITIZED***
✅ Normal fields remain unchanged
✅ Reconstruction replaces sanitized values with empty strings

This test demonstrates that:
1. Only specified secret fields are sanitized
2. Runtime parameters with secret-like values are detected
3. Normal fields are left unchanged
4. Reconstruction works correctly
5. No false positives or false negatives
*/
