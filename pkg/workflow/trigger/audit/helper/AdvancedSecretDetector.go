/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"math"
	"regexp"
	"strings"
	"unicode"
	"go.uber.org/zap"
)

// AdvancedSecretDetector provides sophisticated secret detection
type AdvancedSecretDetector interface {
	// IsSecret determines if a value is likely a secret using multiple techniques
	IsSecret(fieldName string, value string) bool
	
	// GetSecretConfidence returns confidence score (0-100) that value is a secret
	GetSecretConfidence(fieldName string, value string) int
	
	// DetectSecretType identifies the type of secret
	DetectSecretType(value string) string
}

type AdvancedSecretDetectorImpl struct {
	logger           *zap.SugaredLogger
	compiledPatterns []*regexp.Regexp
	entropyThreshold float64
	minSecretLength  int
}

func NewAdvancedSecretDetectorImpl(logger *zap.SugaredLogger) *AdvancedSecretDetectorImpl {
	detector := &AdvancedSecretDetectorImpl{
		logger:           logger,
		entropyThreshold: 4.5, // Shannon entropy threshold
		minSecretLength:  12,
	}
	
	detector.compilePatterns()
	return detector
}

// SecretPattern defines a regex pattern for secret detection
type SecretPattern struct {
	Name        string
	Pattern     string
	Confidence  int // 1-100
	Description string
}

// getSecretPatterns returns comprehensive secret patterns
func (impl *AdvancedSecretDetectorImpl) getSecretPatterns() []SecretPattern {
	return []SecretPattern{
		// High confidence patterns (90-100)
		{"AWS Access Key", `AKIA[0-9A-Z]{16}`, 95, "AWS Access Key ID"},
		{"AWS Secret Key", `[A-Za-z0-9/+=]{40}`, 85, "AWS Secret Access Key"},
		{"GitHub Token", `ghp_[a-zA-Z0-9]{36}`, 98, "GitHub Personal Access Token"},
		{"GitHub OAuth", `gho_[a-zA-Z0-9]{36}`, 98, "GitHub OAuth Token"},
		{"Slack Bot Token", `xoxb-[0-9]{11}-[0-9]{12}-[a-zA-Z0-9]{24}`, 95, "Slack Bot Token"},
		{"Slack User Token", `xoxp-[0-9]{11}-[0-9]{12}-[a-zA-Z0-9]{24}`, 95, "Slack User Token"},
		{"OpenAI API Key", `sk-[a-zA-Z0-9]{48}`, 98, "OpenAI API Key"},
		{"Stripe API Key", `sk_(test|live)_[a-zA-Z0-9]{24}`, 98, "Stripe API Key"},
		
		// Medium confidence patterns (70-89)
		{"JWT Token", `eyJ[a-zA-Z0-9_-]*\.eyJ[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*`, 80, "JWT Token"},
		{"Base64 Key", `[A-Za-z0-9+/]{32,}={0,2}`, 70, "Base64 Encoded Key"},
		{"Hex Key", `[a-fA-F0-9]{32,}`, 75, "Hexadecimal Key"},
		{"UUID", `[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}`, 60, "UUID"},
		
		// Certificate patterns (85-95)
		{"Private Key", `-----BEGIN [A-Z ]*PRIVATE KEY-----`, 95, "Private Key"},
		{"Certificate", `-----BEGIN CERTIFICATE-----`, 90, "Certificate"},
		{"Public Key", `-----BEGIN PUBLIC KEY-----`, 85, "Public Key"},
		
		// API Key patterns (80-90)
		{"Generic API Key", `[Aa][Pp][Ii]_?[Kk][Ee][Yy].*[a-zA-Z0-9]{16,}`, 80, "Generic API Key"},
		{"Bearer Token", `Bearer [a-zA-Z0-9_-]{20,}`, 85, "Bearer Token"},
		{"Basic Auth", `Basic [a-zA-Z0-9+/]+=*`, 85, "Basic Authentication"},
		
		// Database URLs (75-85)
		{"Database URL", `(mysql|postgres|mongodb)://[^:]+:[^@]+@`, 80, "Database Connection String"},
		{"Redis URL", `redis://[^:]*:[^@]*@`, 80, "Redis Connection String"},
		
		// Cloud provider patterns (85-95)
		{"Azure Client Secret", `[a-zA-Z0-9~._-]{34,}`, 70, "Azure Client Secret"},
		{"GCP Service Account", `"type":\s*"service_account"`, 85, "GCP Service Account Key"},
		
		// Generic high-entropy patterns (60-75)
		{"High Entropy String", `[a-zA-Z0-9+/=]{40,}`, 65, "High Entropy String"},
	}
}

func (impl *AdvancedSecretDetectorImpl) compilePatterns() {
	patterns := impl.getSecretPatterns()
	impl.compiledPatterns = make([]*regexp.Regexp, len(patterns))
	
	for i, pattern := range patterns {
		compiled, err := regexp.Compile(pattern.Pattern)
		if err != nil {
			impl.logger.Errorw("failed to compile regex pattern", "pattern", pattern.Name, "err", err)
			continue
		}
		impl.compiledPatterns[i] = compiled
	}
}

func (impl *AdvancedSecretDetectorImpl) IsSecret(fieldName string, value string) bool {
	confidence := impl.GetSecretConfidence(fieldName, value)
	return confidence >= 70 // Threshold for considering something a secret
}

func (impl *AdvancedSecretDetectorImpl) GetSecretConfidence(fieldName string, value string) int {
	if len(value) < impl.minSecretLength {
		return 0
	}
	
	maxConfidence := 0
	
	// 1. Pattern-based detection
	patternConfidence := impl.getPatternConfidence(value)
	if patternConfidence > maxConfidence {
		maxConfidence = patternConfidence
	}
	
	// 2. Entropy-based detection
	entropyConfidence := impl.getEntropyConfidence(value)
	if entropyConfidence > maxConfidence {
		maxConfidence = entropyConfidence
	}
	
	// 3. Field name context
	fieldConfidence := impl.getFieldNameConfidence(fieldName)
	if fieldConfidence > 0 {
		// Boost confidence if field name suggests it's a secret
		maxConfidence = int(math.Min(100, float64(maxConfidence)+float64(fieldConfidence)*0.3))
	}
	
	// 4. Length-based confidence
	lengthConfidence := impl.getLengthConfidence(value)
	maxConfidence = int(math.Min(100, float64(maxConfidence)+float64(lengthConfidence)*0.2))
	
	return maxConfidence
}

func (impl *AdvancedSecretDetectorImpl) getPatternConfidence(value string) int {
	patterns := impl.getSecretPatterns()
	maxConfidence := 0
	
	for i, pattern := range patterns {
		if i < len(impl.compiledPatterns) && impl.compiledPatterns[i] != nil {
			if impl.compiledPatterns[i].MatchString(value) {
				if pattern.Confidence > maxConfidence {
					maxConfidence = pattern.Confidence
				}
			}
		}
	}
	
	return maxConfidence
}

func (impl *AdvancedSecretDetectorImpl) getEntropyConfidence(value string) int {
	entropy := impl.calculateShannonEntropy(value)
	
	if entropy >= 5.5 {
		return 85 // Very high entropy
	} else if entropy >= 5.0 {
		return 75 // High entropy
	} else if entropy >= 4.5 {
		return 65 // Medium-high entropy
	} else if entropy >= 4.0 {
		return 50 // Medium entropy
	}
	
	return 0 // Low entropy
}

func (impl *AdvancedSecretDetectorImpl) calculateShannonEntropy(s string) float64 {
	if len(s) == 0 {
		return 0
	}
	
	// Count character frequencies
	freq := make(map[rune]int)
	for _, char := range s {
		freq[char]++
	}
	
	// Calculate entropy
	entropy := 0.0
	length := float64(len(s))
	
	for _, count := range freq {
		p := float64(count) / length
		if p > 0 {
			entropy -= p * math.Log2(p)
		}
	}
	
	return entropy
}

func (impl *AdvancedSecretDetectorImpl) getFieldNameConfidence(fieldName string) int {
	lowerField := strings.ToLower(fieldName)
	
	// High confidence field names
	highConfidenceFields := []string{
		"password", "secret", "token", "key", "credential",
		"auth", "api_key", "private_key", "access_key",
	}
	
	for _, field := range highConfidenceFields {
		if strings.Contains(lowerField, field) {
			return 80
		}
	}
	
	// Medium confidence field names
	mediumConfidenceFields := []string{
		"cert", "certificate", "pass", "pwd", "oauth",
	}
	
	for _, field := range mediumConfidenceFields {
		if strings.Contains(lowerField, field) {
			return 60
		}
	}
	
	return 0
}

func (impl *AdvancedSecretDetectorImpl) getLengthConfidence(value string) int {
	length := len(value)
	
	if length >= 64 {
		return 30 // Very long strings are often secrets
	} else if length >= 40 {
		return 20 // Long strings might be secrets
	} else if length >= 24 {
		return 10 // Medium strings could be secrets
	}
	
	return 0
}

func (impl *AdvancedSecretDetectorImpl) DetectSecretType(value string) string {
	patterns := impl.getSecretPatterns()
	
	for i, pattern := range patterns {
		if i < len(impl.compiledPatterns) && impl.compiledPatterns[i] != nil {
			if impl.compiledPatterns[i].MatchString(value) {
				return pattern.Name
			}
		}
	}
	
	// Fallback to entropy-based classification
	entropy := impl.calculateShannonEntropy(value)
	if entropy >= 5.0 {
		return "High Entropy Secret"
	} else if entropy >= 4.5 {
		return "Medium Entropy Secret"
	}
	
	return "Unknown"
}

// Additional utility methods

func (impl *AdvancedSecretDetectorImpl) IsBase64(s string) bool {
	// Check if string is valid base64
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil && len(s)%4 == 0
}

func (impl *AdvancedSecretDetectorImpl) HasMixedCase(s string) bool {
	hasUpper := false
	hasLower := false
	
	for _, char := range s {
		if unicode.IsUpper(char) {
			hasUpper = true
		} else if unicode.IsLower(char) {
			hasLower = true
		}
		
		if hasUpper && hasLower {
			return true
		}
	}
	
	return false
}

func (impl *AdvancedSecretDetectorImpl) GetValueHash(value string) string {
	hash := sha256.Sum256([]byte(value))
	return fmt.Sprintf("%x", hash[:8]) // First 8 bytes for logging
}

/*
Usage Example:

detector := NewAdvancedSecretDetectorImpl(logger)

// Test various values
testValues := []string{
    "sk-1234567890abcdef1234567890abcdef12345678", // OpenAI API key
    "AKIAIOSFODNN7EXAMPLE",                         // AWS access key
    "ghp_1234567890abcdef1234567890abcdef123456",   // GitHub token
    "just-a-normal-value",                          // Normal value
    "password123",                                  // Simple password
}

for _, value := range testValues {
    confidence := detector.GetSecretConfidence("API_KEY", value)
    secretType := detector.DetectSecretType(value)
    isSecret := detector.IsSecret("API_KEY", value)
    
    fmt.Printf("Value: %s\n", value[:min(20, len(value))])
    fmt.Printf("Confidence: %d%%\n", confidence)
    fmt.Printf("Type: %s\n", secretType)
    fmt.Printf("Is Secret: %v\n\n", isSecret)
}

Benefits of Advanced Detection:

1. **Higher Accuracy**: Multiple detection techniques
2. **Confidence Scoring**: Know how certain the detection is
3. **Secret Classification**: Identify type of secret
4. **Entropy Analysis**: Detect high-randomness strings
5. **Context Awareness**: Consider field names
6. **Extensible**: Easy to add new patterns
7. **Performance**: Compiled regex patterns
8. **Reduced False Positives**: Multiple validation layers

This approach is much more sophisticated than simple pattern matching!
*/
