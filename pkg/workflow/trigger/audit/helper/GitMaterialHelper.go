/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"encoding/json"
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	auditBean "github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/bean"
	"go.uber.org/zap"
	"time"
)

// GitMaterialHelper provides helper functions for extracting git material information
type GitMaterialHelper interface {
	// ExtractGitMaterialsFromCiTrigger extracts git materials from CI trigger
	ExtractGitMaterialsFromCiTrigger(trigger types.Trigger) ([]auditBean.GitMaterial, error)

	// ExtractGitMaterialsFromCiArtifact extracts git materials from CI artifact
	ExtractGitMaterialsFromCiArtifact(artifact *repository.CiArtifact) ([]auditBean.GitMaterial, error)

	// ExtractGitMaterialsFromCiWorkflow extracts git materials from CI workflow
	ExtractGitMaterialsFromCiWorkflow(ciWorkflow *pipelineConfig.CiWorkflow) ([]auditBean.GitMaterial, error)

	// ExtractGitMaterialsFromCommitHashes extracts git materials from commit hashes
	ExtractGitMaterialsFromCommitHashes(commitHashes map[int]pipelineConfig.GitCommit) ([]auditBean.GitMaterial, error)
}

type GitMaterialHelperImpl struct {
	logger *zap.SugaredLogger
}

func NewGitMaterialHelperImpl(logger *zap.SugaredLogger) *GitMaterialHelperImpl {
	return &GitMaterialHelperImpl{
		logger: logger,
	}
}

func (impl *GitMaterialHelperImpl) ExtractGitMaterialsFromCiTrigger(trigger types.Trigger) ([]auditBean.GitMaterial, error) {
	var gitMaterials []auditBean.GitMaterial

	// Extract from commit hashes
	for materialId, commitHash := range trigger.CommitHashes {
		material := auditBean.GitMaterial{
			GitMaterialId: materialId,
			CommitHash:    commitHash.Commit,
			CommitMessage: commitHash.Message,
			CommitAuthor:  commitHash.Author,
			CommitDate:    commitHash.Date,
			Branch:        commitHash.GitTag, // GitTag field is used for branch in some contexts
		}
		gitMaterials = append(gitMaterials, material)
	}

	// Extract from CI materials if available
	for _, ciMaterial := range trigger.CiMaterials {
		// Find if we already have this material from commit hashes
		found := false
		for i, existing := range gitMaterials {
			if existing.GitMaterialId == ciMaterial.GitMaterialId {
				// Update with additional information from CI material
				gitMaterials[i].Branch = ciMaterial.Source.Value
				gitMaterials[i].Tag = ciMaterial.Source.Value // Could be tag or branch
				found = true
				break
			}
		}

		if !found {
			// Create new material entry
			material := auditBean.GitMaterial{
				GitMaterialId: ciMaterial.GitMaterialId,
				Branch:        ciMaterial.Source.Value,
				Tag:           ciMaterial.Source.Value,
			}
			gitMaterials = append(gitMaterials, material)
		}
	}

	return gitMaterials, nil
}

func (impl *GitMaterialHelperImpl) ExtractGitMaterialsFromCiArtifact(artifact *repository.CiArtifact) ([]auditBean.GitMaterial, error) {
	var gitMaterials []auditBean.GitMaterial

	if artifact.MaterialInfo == "" {
		impl.logger.Warnw("no material info found in CI artifact", "artifactId", artifact.Id)
		return gitMaterials, nil
	}

	// Parse material info JSON
	var materialInfos []bean.CiMaterialInfo
	err := json.Unmarshal([]byte(artifact.MaterialInfo), &materialInfos)
	if err != nil {
		impl.logger.Errorw("error in unmarshaling material info from CI artifact", "err", err, "artifactId", artifact.Id)
		return nil, err
	}

	for _, materialInfo := range materialInfos {
		material := auditBean.GitMaterial{
			GitMaterialId: materialInfo.Material.GitMaterialId,
			CommitHash:    materialInfo.Material.GitCommit.Commit,
			CommitMessage: materialInfo.Material.GitCommit.Message,
			CommitAuthor:  materialInfo.Material.GitCommit.Author,
			CommitDate:    materialInfo.Material.GitCommit.Date,
			Branch:        materialInfo.Material.GitCommit.GitTag, // GitTag field is used for branch
		}

		// Add webhook data if available
		if materialInfo.Material.GitCommit.WebhookData != nil {
			material.WebhookData = materialInfo.Material.GitCommit.WebhookData
		}

		gitMaterials = append(gitMaterials, material)
	}

	return gitMaterials, nil
}

func (impl *GitMaterialHelperImpl) ExtractGitMaterialsFromCiWorkflow(ciWorkflow *pipelineConfig.CiWorkflow) ([]auditBean.GitMaterial, error) {
	var gitMaterials []auditBean.GitMaterial

	if ciWorkflow.GitTriggers == nil {
		impl.logger.Warnw("no git triggers found in CI workflow", "workflowId", ciWorkflow.Id)
		return gitMaterials, nil
	}

	return impl.ExtractGitMaterialsFromCommitHashes(ciWorkflow.GitTriggers)
}

func (impl *GitMaterialHelperImpl) ExtractGitMaterialsFromCommitHashes(commitHashes map[int]pipelineConfig.GitCommit) ([]auditBean.GitMaterial, error) {
	var gitMaterials []auditBean.GitMaterial

	for materialId, gitCommit := range commitHashes {
		material := auditBean.GitMaterial{
			GitMaterialId: materialId,
			CommitHash:    gitCommit.Commit,
			CommitMessage: gitCommit.Message,
			CommitAuthor:  gitCommit.Author,
			CommitDate:    gitCommit.Date,
			Branch:        gitCommit.GitTag, // GitTag field is used for branch in some contexts
		}

		// Add webhook data if available
		if gitCommit.WebhookData != nil {
			material.WebhookData = gitCommit.WebhookData
		}

		gitMaterials = append(gitMaterials, material)
	}

	return gitMaterials, nil
}

// Helper function to determine trigger type based on context
func DetermineTriggerType(isManual bool, isWebhook bool) string {
	if isWebhook {
		return "WEBHOOK"
	}
	if isManual {
		return "MANUAL"
	}
	return "AUTO"
}

// Helper function to create trigger metadata
func CreateTriggerMetadata(additionalData map[string]interface{}) map[string]interface{} {
	metadata := map[string]interface{}{
		"timestamp": time.Now(),
	}

	// Add additional data if provided
	for key, value := range additionalData {
		metadata[key] = value
	}

	return metadata
}

// Helper function to extract environment information from CD pipeline
func ExtractEnvironmentInfo(pipeline *pipelineConfig.Pipeline) map[string]interface{} {
	return map[string]interface{}{
		"environmentId":   pipeline.EnvironmentId,
		"deploymentAppType": pipeline.DeploymentAppType,
		"triggerType":     pipeline.TriggerType,
	}
}

// Helper function to extract CI pipeline information
func ExtractCiPipelineInfo(pipeline *pipelineConfig.CiPipeline) map[string]interface{} {
	return map[string]interface{}{
		"pipelineType": pipeline.PipelineType,
		"isManual":     pipeline.IsManual,
		"isExternal":   pipeline.IsExternal,
	}
}

// Helper function to validate git materials
func ValidateGitMaterials(gitMaterials []auditBean.GitMaterial) error {
	for i, material := range gitMaterials {
		if material.GitMaterialId == 0 {
			return fmt.Errorf("git material at index %d has invalid GitMaterialId", i)
		}
		if material.CommitHash == "" {
			return fmt.Errorf("git material at index %d has empty CommitHash", i)
		}
	}
	return nil
}

// Helper function to merge git materials from different sources
func MergeGitMaterials(materials1, materials2 []auditBean.GitMaterial) []auditBean.GitMaterial {
	materialMap := make(map[int]auditBean.GitMaterial)

	// Add materials from first source
	for _, material := range materials1 {
		materialMap[material.GitMaterialId] = material
	}

	// Merge materials from second source
	for _, material := range materials2 {
		if existing, exists := materialMap[material.GitMaterialId]; exists {
			// Merge the materials, preferring non-empty values from the second source
			merged := existing
			if material.CommitHash != "" {
				merged.CommitHash = material.CommitHash
			}
			if material.CommitMessage != "" {
				merged.CommitMessage = material.CommitMessage
			}
			if material.CommitAuthor != "" {
				merged.CommitAuthor = material.CommitAuthor
			}
			if !material.CommitDate.IsZero() {
				merged.CommitDate = material.CommitDate
			}
			if material.Branch != "" {
				merged.Branch = material.Branch
			}
			if material.Tag != "" {
				merged.Tag = material.Tag
			}
			if material.WebhookData != nil {
				merged.WebhookData = material.WebhookData
			}
			materialMap[material.GitMaterialId] = merged
		} else {
			materialMap[material.GitMaterialId] = material
		}
	}

	// Convert map back to slice
	var result []auditBean.GitMaterial
	for _, material := range materialMap {
		result = append(result, material)
	}

	return result
}
