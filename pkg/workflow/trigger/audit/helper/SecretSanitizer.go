/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"encoding/json"
	"go.uber.org/zap"
	"strings"
)

const (
	SANITIZED_SECRET_PLACEHOLDER = "***SANITIZED***"
	SECRET_REFERENCE_PREFIX      = "***SECRET_REF:"
	SECRET_REFERENCE_SUFFIX      = "***"
)

// SecretSanitizer handles sanitization and reconstruction of secrets in WorkflowRequest
type SecretSanitizer interface {
	// SanitizeWorkflowRequest removes secret values and stores references
	SanitizeWorkflowRequest(workflowRequest interface{}) (interface{}, error)

	// ReconstructSecrets fills sanitized secrets with current values from environment
	ReconstructSecrets(sanitizedWorkflowRequest interface{}, environmentId *int, appId int) (interface{}, error)

	// SanitizeJSON sanitizes secrets in JSON string
	SanitizeJSON(jsonStr string) (string, error)

	// ReconstructJSON reconstructs secrets in JSON string
	ReconstructJSON(sanitizedJsonStr string, environmentId *int, appId int) (string, error)
}

type SecretSanitizerImpl struct {
	logger *zap.SugaredLogger
	// Add dependencies for fetching current secret values
	// configMapService ConfigMapService
	// secretService    SecretService
}

func NewSecretSanitizerImpl(logger *zap.SugaredLogger) *SecretSanitizerImpl {
	return &SecretSanitizerImpl{
		logger: logger,
	}
}

// SecretReference stores metadata about sanitized secrets
type SecretReference struct {
	Type          string `json:"type"`          // "configmap", "secret", "env_var"
	Name          string `json:"name"`          // name of the secret/configmap
	Key           string `json:"key"`           // key within the secret/configmap
	Namespace     string `json:"namespace"`     // namespace (for k8s secrets)
	AppId         int    `json:"appId"`         // app context
	EnvironmentId *int   `json:"environmentId"` // environment context
}

func (impl *SecretSanitizerImpl) SanitizeWorkflowRequest(workflowRequest interface{}) (interface{}, error) {
	impl.logger.Infow("sanitizing secrets in workflow request")

	// Convert to JSON for easier manipulation
	jsonBytes, err := json.Marshal(workflowRequest)
	if err != nil {
		impl.logger.Errorw("error marshaling workflow request for sanitization", "err", err)
		return nil, err
	}

	// Parse as generic map for manipulation
	var workflowMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &workflowMap)
	if err != nil {
		impl.logger.Errorw("error unmarshaling workflow request for sanitization", "err", err)
		return nil, err
	}

	// Sanitize different types of secrets
	sanitizedMap := impl.sanitizeMap(workflowMap)

	impl.logger.Infow("successfully sanitized secrets in workflow request")
	return sanitizedMap, nil
}

func (impl *SecretSanitizerImpl) sanitizeMap(data map[string]interface{}) map[string]interface{} {
	return impl.sanitizeMapWithPath(data, "")
}

func (impl *SecretSanitizerImpl) sanitizeMapWithPath(data map[string]interface{}, parentPath string) map[string]interface{} {
	sanitized := make(map[string]interface{})

	for key, value := range data {
		// Build full field path for nested field detection
		fullPath := key
		if parentPath != "" {
			fullPath = parentPath + "." + key
		}

		sanitized[key] = impl.sanitizeValueWithPath(fullPath, value)
	}

	return sanitized
}

func (impl *SecretSanitizerImpl) sanitizeValueWithPath(fullPath string, value interface{}) interface{} {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return impl.sanitizeStringValueWithPath(fullPath, v)
	case map[string]interface{}:
		return impl.sanitizeMapWithPath(v, fullPath)
	case []interface{}:
		return impl.sanitizeArrayWithPath(v, fullPath)
	default:
		return value
	}
}

func (impl *SecretSanitizerImpl) sanitizeArrayWithPath(arr []interface{}, parentPath string) []interface{} {
	sanitized := make([]interface{}, len(arr))
	for i, item := range arr {
		if itemMap, ok := item.(map[string]interface{}); ok {
			sanitized[i] = impl.sanitizeMapWithPath(itemMap, parentPath)
		} else {
			sanitized[i] = item
		}
	}
	return sanitized
}

func (impl *SecretSanitizerImpl) sanitizeStringValueWithPath(fullPath string, value string) interface{} {
	// Check if this is a known secret field
	if impl.isKnownSecretField(fullPath) {
		impl.logger.Debugw("sanitizing secret field", "field", fullPath, "originalLength", len(value))
		return SANITIZED_SECRET_PLACEHOLDER
	}

	// Special handling for runtime parameters
	if impl.isRuntimeParameterSecret(fullPath, value) {
		impl.logger.Debugw("sanitizing runtime parameter secret", "field", fullPath, "originalLength", len(value))
		return SANITIZED_SECRET_PLACEHOLDER
	}

	return value
}

func (impl *SecretSanitizerImpl) isKnownSecretField(fieldName string) bool {
	// Exact field names that should be sanitized
	knownSecretFields := map[string]bool{
		// Docker registry secrets
		"dockerPassword":           true,
		"docker_password":          true,

		// AWS/S3 secrets
		"accessKey":               true,
		"access_key":              true,
		"secretKey":               true,
		"secret_key":              true,
		"passkey":                 true,
		"pass_key":                true,

		// Docker certificates
		"dockerCert":              true,
		"docker_cert":             true,
		"dockerCertificate":       true,
		"docker_certificate":      true,

		// Azure blob storage
		"accountKey":              true,
		"account_key":             true,

		// GCP blob storage
		"credentialFileData":      true,
		"credential_file_data":    true,
		"serviceAccountKey":       true,
		"service_account_key":     true,

		// Additional common secret fields
		"password":                true,
		"token":                   true,
		"apiKey":                  true,
		"api_key":                 true,
		"privateKey":              true,
		"private_key":             true,
		"clientSecret":            true,
		"client_secret":           true,
	}

	// Check exact field name
	if knownSecretFields[fieldName] {
		return true
	}

	// Check for nested field paths (e.g., "blobStorageS3Config.accessKey")
	return impl.isNestedSecretField(fieldName)
}

func (impl *SecretSanitizerImpl) isNestedSecretField(fieldPath string) bool {
	// Define nested secret field patterns
	nestedSecretPatterns := []string{
		// Blob storage configurations
		"blobStorageS3Config.accessKey",
		"blobStorageS3Config.secretKey",
		"blobStorageS3Config.passkey",
		"blobStorageS3Config.password",

		"azureBlobConfig.accountKey",
		"azureBlobConfig.accountName",
		"azureBlobConfig.password",

		"gcpBlobConfig.credentialFileData",
		"gcpBlobConfig.serviceAccountKey",
		"gcpBlobConfig.privateKey",

		// Docker registry configurations
		"dockerRegistry.password",
		"dockerRegistry.dockerPassword",
		"dockerRegistry.accessKey",
		"dockerRegistry.secretKey",
		"dockerRegistry.cert",
		"dockerRegistry.certificate",

		// Database configurations
		"database.password",
		"database.connectionString",
		"database.url",

		// External service configurations
		"externalService.apiKey",
		"externalService.token",
		"externalService.secret",
		"externalService.password",

		// CI/CD tool configurations
		"gitCredentials.password",
		"gitCredentials.token",
		"gitCredentials.privateKey",

		// Cloud provider configurations
		"awsConfig.secretAccessKey",
		"awsConfig.accessKeyId",
		"gcpConfig.serviceAccountKey",
		"azureConfig.clientSecret",
		"azureConfig.password",
	}

	// Check if the field path matches any known nested secret pattern
	for _, pattern := range nestedSecretPatterns {
		if strings.HasSuffix(fieldPath, pattern) || fieldPath == pattern {
			return true
		}
	}

	// Check if any part of the path contains secret field names
	pathParts := strings.Split(fieldPath, ".")
	if len(pathParts) > 1 {
		lastPart := pathParts[len(pathParts)-1]
		return impl.isKnownSecretField(lastPart)
	}

	return false
}

func (impl *SecretSanitizerImpl) isRuntimeParameterSecret(fieldPath string, value string) bool {
	// Check if this is within runtime parameters
	if !strings.Contains(fieldPath, "runtimeParameters") &&
	   !strings.Contains(fieldPath, "runtime_parameters") &&
	   !strings.Contains(fieldPath, "RuntimeParameters") {
		return false
	}

	// Simple heuristics for runtime parameters
	return impl.looksLikeSecret(value)
}

func (impl *SecretSanitizerImpl) looksLikeSecret(value string) bool {
	// Skip short values
	if len(value) < 12 {
		return false
	}

	// Check if field name suggests it's a secret
	lowerValue := strings.ToLower(value)
	if strings.Contains(lowerValue, "password") ||
	   strings.Contains(lowerValue, "secret") ||
	   strings.Contains(lowerValue, "token") ||
	   strings.Contains(lowerValue, "key") {
		return false // These are likely field names, not values
	}

	// Simple patterns that are very likely secrets
	if strings.HasPrefix(value, "sk-") ||      // OpenAI, Stripe keys
	   strings.HasPrefix(value, "ghp_") ||     // GitHub tokens
	   strings.HasPrefix(value, "xoxb-") ||    // Slack tokens
	   strings.HasPrefix(value, "AKIA") ||     // AWS keys
	   strings.Contains(value, "-----BEGIN") { // Certificates
		return true
	}

	// If it's long and looks random (no spaces, mixed case/numbers)
	if len(value) > 20 && !strings.Contains(value, " ") {
		hasUpper := false
		hasLower := false
		hasDigit := false

		for _, char := range value {
			if char >= 'A' && char <= 'Z' {
				hasUpper = true
			} else if char >= 'a' && char <= 'z' {
				hasLower = true
			} else if char >= '0' && char <= '9' {
				hasDigit = true
			}
		}

		// If it has mixed case and numbers, likely a secret
		return hasUpper && hasLower && hasDigit
	}

	return false
}



func (impl *SecretSanitizerImpl) createSecretReference(key string, value string) *SecretReference {
	// Try to extract secret reference information from context
	// This would need to be enhanced based on your WorkflowRequest structure

	// For now, return a basic reference
	return &SecretReference{
		Type: "unknown",
		Name: key,
		Key:  key,
	}
}

func (impl *SecretSanitizerImpl) ReconstructSecrets(sanitizedWorkflowRequest interface{}, environmentId *int, appId int) (interface{}, error) {
	impl.logger.Infow("reconstructing secrets in workflow request", "appId", appId, "environmentId", environmentId)

	// Convert to JSON for manipulation
	jsonBytes, err := json.Marshal(sanitizedWorkflowRequest)
	if err != nil {
		return nil, err
	}

	// Parse as generic map
	var workflowMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &workflowMap)
	if err != nil {
		return nil, err
	}

	// Reconstruct secrets
	reconstructedMap := impl.reconstructMap(workflowMap, environmentId, appId)

	impl.logger.Infow("successfully reconstructed secrets in workflow request")
	return reconstructedMap, nil
}

func (impl *SecretSanitizerImpl) reconstructMap(data map[string]interface{}, environmentId *int, appId int) map[string]interface{} {
	return impl.reconstructMapWithPath(data, "", environmentId, appId)
}

func (impl *SecretSanitizerImpl) reconstructMapWithPath(data map[string]interface{}, parentPath string, environmentId *int, appId int) map[string]interface{} {
	reconstructed := make(map[string]interface{})

	for key, value := range data {
		// Build full field path for nested field detection
		fullPath := key
		if parentPath != "" {
			fullPath = parentPath + "." + key
		}

		reconstructed[key] = impl.reconstructValueWithPath(fullPath, value, environmentId, appId)
	}

	return reconstructed
}

func (impl *SecretSanitizerImpl) reconstructValueWithPath(fullPath string, value interface{}, environmentId *int, appId int) interface{} {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return impl.reconstructStringValueWithPath(fullPath, v, environmentId, appId)
	case map[string]interface{}:
		return impl.reconstructMapWithPath(v, fullPath, environmentId, appId)
	case []interface{}:
		return impl.reconstructArrayWithPath(v, fullPath, environmentId, appId)
	default:
		return value
	}
}

func (impl *SecretSanitizerImpl) reconstructArrayWithPath(arr []interface{}, parentPath string, environmentId *int, appId int) []interface{} {
	reconstructed := make([]interface{}, len(arr))
	for i, item := range arr {
		reconstructed[i] = impl.reconstructValueWithPath(parentPath, item, environmentId, appId)
	}
	return reconstructed
}

func (impl *SecretSanitizerImpl) reconstructStringValueWithPath(fullPath string, value string, environmentId *int, appId int) interface{} {
	// Check if it's a sanitized placeholder
	if value == SANITIZED_SECRET_PLACEHOLDER {
		// For field-specific approach, we know this was a secret field
		// Return empty string as we don't store references for reconstruction
		impl.logger.Debugw("reconstructing sanitized secret field", "field", fullPath)
		return ""
	}

	// Check if it's a secret reference (legacy support)
	if strings.HasPrefix(value, SECRET_REFERENCE_PREFIX) && strings.HasSuffix(value, SECRET_REFERENCE_SUFFIX) {
		refJson := value[len(SECRET_REFERENCE_PREFIX) : len(value)-len(SECRET_REFERENCE_SUFFIX)]

		var secretRef SecretReference
		err := json.Unmarshal([]byte(refJson), &secretRef)
		if err != nil {
			impl.logger.Errorw("error unmarshaling secret reference", "err", err)
			return ""
		}

		// Fetch current secret value
		currentValue := impl.fetchCurrentSecretValue(&secretRef, environmentId, appId)
		return currentValue
	}

	return value
}

func (impl *SecretSanitizerImpl) fetchCurrentSecretValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// This method would integrate with your secret management system
	// to fetch current values of secrets/configmaps

	impl.logger.Infow("fetching current secret value",
		"type", secretRef.Type,
		"name", secretRef.Name,
		"key", secretRef.Key,
		"appId", appId,
		"environmentId", environmentId)

	// TODO: Implement actual secret fetching logic
	// Examples:
	// - Fetch from ConfigMap service
	// - Fetch from Secret service
	// - Fetch from environment variables
	// - Fetch from external secret management (Vault, etc.)

	switch secretRef.Type {
	case "configmap":
		return impl.fetchConfigMapValue(secretRef, environmentId, appId)
	case "secret":
		return impl.fetchSecretValue(secretRef, environmentId, appId)
	case "env_var":
		return impl.fetchEnvironmentVariable(secretRef, environmentId, appId)
	default:
		impl.logger.Warnw("unknown secret type", "type", secretRef.Type)
		return ""
	}
}

func (impl *SecretSanitizerImpl) fetchConfigMapValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with ConfigMapService to fetch current value
	// return impl.configMapService.GetConfigMapValue(appId, environmentId, secretRef.Name, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) fetchSecretValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with SecretService to fetch current value
	// return impl.secretService.GetSecretValue(appId, environmentId, secretRef.Name, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) fetchEnvironmentVariable(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with environment variable service
	// return impl.envVarService.GetEnvironmentVariable(appId, environmentId, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) SanitizeJSON(jsonStr string) (string, error) {
	var data interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return "", err
	}

	sanitized, err := impl.SanitizeWorkflowRequest(data)
	if err != nil {
		return "", err
	}

	sanitizedBytes, err := json.Marshal(sanitized)
	if err != nil {
		return "", err
	}

	return string(sanitizedBytes), nil
}

func (impl *SecretSanitizerImpl) ReconstructJSON(sanitizedJsonStr string, environmentId *int, appId int) (string, error) {
	var data interface{}
	err := json.Unmarshal([]byte(sanitizedJsonStr), &data)
	if err != nil {
		return "", err
	}

	reconstructed, err := impl.ReconstructSecrets(data, environmentId, appId)
	if err != nil {
		return "", err
	}

	reconstructedBytes, err := json.Marshal(reconstructed)
	if err != nil {
		return "", err
	}

	return string(reconstructedBytes), nil
}
