/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"encoding/json"
	"go.uber.org/zap"
	"strings"
)

const (
	SANITIZED_SECRET_PLACEHOLDER = "***SANITIZED***"
	SECRET_REFERENCE_PREFIX      = "***SECRET_REF:"
	SECRET_REFERENCE_SUFFIX      = "***"
)

// SecretSanitizer handles sanitization and reconstruction of secrets in WorkflowRequest
type SecretSanitizer interface {
	// SanitizeWorkflowRequest removes secret values and stores references
	SanitizeWorkflowRequest(workflowRequest interface{}) (interface{}, error)

	// ReconstructSecrets fills sanitized secrets with current values from environment
	ReconstructSecrets(sanitizedWorkflowRequest interface{}, environmentId *int, appId int) (interface{}, error)

	// SanitizeJSON sanitizes secrets in JSON string
	SanitizeJSON(jsonStr string) (string, error)

	// ReconstructJSON reconstructs secrets in JSON string
	ReconstructJSON(sanitizedJsonStr string, environmentId *int, appId int) (string, error)
}

type SecretSanitizerImpl struct {
	logger *zap.SugaredLogger
	// Add dependencies for fetching current secret values
	// configMapService ConfigMapService
	// secretService    SecretService
}

func NewSecretSanitizerImpl(logger *zap.SugaredLogger) *SecretSanitizerImpl {
	return &SecretSanitizerImpl{
		logger: logger,
	}
}

// SecretReference stores metadata about sanitized secrets
type SecretReference struct {
	Type          string `json:"type"`          // "configmap", "secret", "env_var"
	Name          string `json:"name"`          // name of the secret/configmap
	Key           string `json:"key"`           // key within the secret/configmap
	Namespace     string `json:"namespace"`     // namespace (for k8s secrets)
	AppId         int    `json:"appId"`         // app context
	EnvironmentId *int   `json:"environmentId"` // environment context
}

func (impl *SecretSanitizerImpl) SanitizeWorkflowRequest(workflowRequest interface{}) (interface{}, error) {
	impl.logger.Infow("sanitizing secrets in workflow request")

	// Convert to JSON for easier manipulation
	jsonBytes, err := json.Marshal(workflowRequest)
	if err != nil {
		impl.logger.Errorw("error marshaling workflow request for sanitization", "err", err)
		return nil, err
	}

	// Parse as generic map for manipulation
	var workflowMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &workflowMap)
	if err != nil {
		impl.logger.Errorw("error unmarshaling workflow request for sanitization", "err", err)
		return nil, err
	}

	// Sanitize different types of secrets
	sanitizedMap := impl.sanitizeMap(workflowMap)

	impl.logger.Infow("successfully sanitized secrets in workflow request")
	return sanitizedMap, nil
}

func (impl *SecretSanitizerImpl) sanitizeMap(data map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})

	for key, value := range data {
		sanitized[key] = impl.sanitizeValue(key, value)
	}

	return sanitized
}

func (impl *SecretSanitizerImpl) sanitizeValue(key string, value interface{}) interface{} {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return impl.sanitizeStringValue(key, v)
	case map[string]interface{}:
		return impl.sanitizeMap(v)
	case []interface{}:
		return impl.sanitizeArray(v)
	default:
		return value
	}
}

func (impl *SecretSanitizerImpl) sanitizeArray(arr []interface{}) []interface{} {
	sanitized := make([]interface{}, len(arr))
	for i, item := range arr {
		if itemMap, ok := item.(map[string]interface{}); ok {
			sanitized[i] = impl.sanitizeMap(itemMap)
		} else {
			sanitized[i] = item
		}
	}
	return sanitized
}

func (impl *SecretSanitizerImpl) sanitizeStringValue(key string, value string) interface{} {
	lowerKey := strings.ToLower(key)

	// Check if this field contains secrets based on key name
	if impl.isSecretField(lowerKey) {
		// Create secret reference instead of storing actual value
		secretRef := impl.createSecretReference(key, value)
		if secretRef != nil {
			refJson, _ := json.Marshal(secretRef)
			return SECRET_REFERENCE_PREFIX + string(refJson) + SECRET_REFERENCE_SUFFIX
		}
		return SANITIZED_SECRET_PLACEHOLDER
	}

	// Check if value looks like a secret (base64, long random strings, etc.)
	if impl.looksLikeSecret(value) {
		return SANITIZED_SECRET_PLACEHOLDER
	}

	return value
}

func (impl *SecretSanitizerImpl) isSecretField(fieldName string) bool {
	secretFields := []string{
		"password", "passwd", "pwd",
		"secret", "secrets",
		"token", "tokens", "auth_token", "access_token", "refresh_token",
		"key", "private_key", "public_key", "api_key", "secret_key",
		"credential", "credentials", "cred",
		"cert", "certificate", "tls",
		"oauth", "jwt",
		"database_url", "db_url", "connection_string",
		"webhook_secret", "signing_secret",
		"encryption_key", "decrypt_key",
	}

	for _, secretField := range secretFields {
		if strings.Contains(fieldName, secretField) {
			return true
		}
	}

	return false
}

func (impl *SecretSanitizerImpl) looksLikeSecret(value string) bool {
	// Skip short values
	if len(value) < 8 {
		return false
	}

	// Check for common secret patterns
	secretPatterns := []string{
		"sk-", "pk-", "rk-", // API key prefixes
		"-----BEGIN", "-----END", // Certificate patterns
		"Bearer ", "Basic ", // Auth headers
		"ghp_", "gho_", "ghu_", "ghs_", // GitHub tokens
		"xoxb-", "xoxp-", "xapp-", // Slack tokens
		"AKIA", "ASIA", // AWS access keys
	}

	for _, pattern := range secretPatterns {
		if strings.Contains(value, pattern) {
			return true
		}
	}

	// Check if it's a long base64-like string
	if len(value) > 32 && impl.isBase64Like(value) {
		return true
	}

	return false
}

func (impl *SecretSanitizerImpl) isBase64Like(value string) bool {
	// Simple heuristic: mostly alphanumeric with +, /, = characters
	allowedChars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
	nonAlphaCount := 0

	for _, char := range value {
		if !strings.ContainsRune(allowedChars, char) {
			return false
		}
		if strings.ContainsRune("+/=", char) {
			nonAlphaCount++
		}
	}

	// If it has some base64 special characters, likely base64
	return nonAlphaCount > 0 && nonAlphaCount < len(value)/4
}

func (impl *SecretSanitizerImpl) createSecretReference(key string, value string) *SecretReference {
	// Try to extract secret reference information from context
	// This would need to be enhanced based on your WorkflowRequest structure

	// For now, return a basic reference
	return &SecretReference{
		Type: "unknown",
		Name: key,
		Key:  key,
	}
}

func (impl *SecretSanitizerImpl) ReconstructSecrets(sanitizedWorkflowRequest interface{}, environmentId *int, appId int) (interface{}, error) {
	impl.logger.Infow("reconstructing secrets in workflow request", "appId", appId, "environmentId", environmentId)

	// Convert to JSON for manipulation
	jsonBytes, err := json.Marshal(sanitizedWorkflowRequest)
	if err != nil {
		return nil, err
	}

	// Parse as generic map
	var workflowMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &workflowMap)
	if err != nil {
		return nil, err
	}

	// Reconstruct secrets
	reconstructedMap := impl.reconstructMap(workflowMap, environmentId, appId)

	impl.logger.Infow("successfully reconstructed secrets in workflow request")
	return reconstructedMap, nil
}

func (impl *SecretSanitizerImpl) reconstructMap(data map[string]interface{}, environmentId *int, appId int) map[string]interface{} {
	reconstructed := make(map[string]interface{})

	for key, value := range data {
		reconstructed[key] = impl.reconstructValue(value, environmentId, appId)
	}

	return reconstructed
}

func (impl *SecretSanitizerImpl) reconstructValue(value interface{}, environmentId *int, appId int) interface{} {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case string:
		return impl.reconstructStringValue(v, environmentId, appId)
	case map[string]interface{}:
		return impl.reconstructMap(v, environmentId, appId)
	case []interface{}:
		return impl.reconstructArray(v, environmentId, appId)
	default:
		return value
	}
}

func (impl *SecretSanitizerImpl) reconstructArray(arr []interface{}, environmentId *int, appId int) []interface{} {
	reconstructed := make([]interface{}, len(arr))
	for i, item := range arr {
		reconstructed[i] = impl.reconstructValue(item, environmentId, appId)
	}
	return reconstructed
}

func (impl *SecretSanitizerImpl) reconstructStringValue(value string, environmentId *int, appId int) interface{} {
	// Check if it's a sanitized placeholder
	if value == SANITIZED_SECRET_PLACEHOLDER {
		// Return empty string or fetch from current environment
		// This would need integration with your secret management system
		return ""
	}

	// Check if it's a secret reference
	if strings.HasPrefix(value, SECRET_REFERENCE_PREFIX) && strings.HasSuffix(value, SECRET_REFERENCE_SUFFIX) {
		refJson := value[len(SECRET_REFERENCE_PREFIX) : len(value)-len(SECRET_REFERENCE_SUFFIX)]

		var secretRef SecretReference
		err := json.Unmarshal([]byte(refJson), &secretRef)
		if err != nil {
			impl.logger.Errorw("error unmarshaling secret reference", "err", err)
			return ""
		}

		// Fetch current secret value
		currentValue := impl.fetchCurrentSecretValue(&secretRef, environmentId, appId)
		return currentValue
	}

	return value
}

func (impl *SecretSanitizerImpl) fetchCurrentSecretValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// This method would integrate with your secret management system
	// to fetch current values of secrets/configmaps

	impl.logger.Infow("fetching current secret value",
		"type", secretRef.Type,
		"name", secretRef.Name,
		"key", secretRef.Key,
		"appId", appId,
		"environmentId", environmentId)

	// TODO: Implement actual secret fetching logic
	// Examples:
	// - Fetch from ConfigMap service
	// - Fetch from Secret service
	// - Fetch from environment variables
	// - Fetch from external secret management (Vault, etc.)

	switch secretRef.Type {
	case "configmap":
		return impl.fetchConfigMapValue(secretRef, environmentId, appId)
	case "secret":
		return impl.fetchSecretValue(secretRef, environmentId, appId)
	case "env_var":
		return impl.fetchEnvironmentVariable(secretRef, environmentId, appId)
	default:
		impl.logger.Warnw("unknown secret type", "type", secretRef.Type)
		return ""
	}
}

func (impl *SecretSanitizerImpl) fetchConfigMapValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with ConfigMapService to fetch current value
	// return impl.configMapService.GetConfigMapValue(appId, environmentId, secretRef.Name, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) fetchSecretValue(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with SecretService to fetch current value
	// return impl.secretService.GetSecretValue(appId, environmentId, secretRef.Name, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) fetchEnvironmentVariable(secretRef *SecretReference, environmentId *int, appId int) string {
	// TODO: Integrate with environment variable service
	// return impl.envVarService.GetEnvironmentVariable(appId, environmentId, secretRef.Key)
	return ""
}

func (impl *SecretSanitizerImpl) SanitizeJSON(jsonStr string) (string, error) {
	var data interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return "", err
	}

	sanitized, err := impl.SanitizeWorkflowRequest(data)
	if err != nil {
		return "", err
	}

	sanitizedBytes, err := json.Marshal(sanitized)
	if err != nil {
		return "", err
	}

	return string(sanitizedBytes), nil
}

func (impl *SecretSanitizerImpl) ReconstructJSON(sanitizedJsonStr string, environmentId *int, appId int) (string, error) {
	var data interface{}
	err := json.Unmarshal([]byte(sanitizedJsonStr), &data)
	if err != nil {
		return "", err
	}

	reconstructed, err := impl.ReconstructSecrets(data, environmentId, appId)
	if err != nil {
		return "", err
	}

	reconstructedBytes, err := json.Marshal(reconstructed)
	if err != nil {
		return "", err
	}

	return string(reconstructedBytes), nil
}
