/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package config

// FieldSpecificSecretConfig defines exact fields that should be sanitized
type FieldSpecificSecretConfig struct {
	// Enable/Disable field-specific sanitization
	EnableFieldSpecificSanitization bool `env:"AUDIT_ENABLE_FIELD_SPECIFIC_SANITIZATION" envDefault:"true"`
	
	// Enable/Disable runtime parameter secret detection
	EnableRuntimeParameterDetection bool `env:"AUDIT_ENABLE_RUNTIME_PARAMETER_DETECTION" envDefault:"true"`
	
	// Minimum length for runtime parameter secret detection
	RuntimeParameterMinLength int `env:"AUDIT_RUNTIME_PARAMETER_MIN_LENGTH" envDefault:"12"`
	
	// Log sanitization actions for debugging
	LogSanitizationActions bool `env:"AUDIT_LOG_SANITIZATION_ACTIONS" envDefault:"false"`
	
	// Additional custom secret fields (comma-separated)
	CustomSecretFields string `env:"AUDIT_CUSTOM_SECRET_FIELDS" envDefault:""`
}

// SecretFieldDefinition defines the exact fields that should be sanitized
type SecretFieldDefinition struct {
	// Docker registry secrets
	DockerSecrets []string
	
	// Cloud storage secrets (S3, Azure, GCP)
	StorageSecrets []string
	
	// Database secrets
	DatabaseSecrets []string
	
	// API and authentication secrets
	AuthSecrets []string
	
	// Certificate and key secrets
	CertificateSecrets []string
	
	// Custom application secrets
	CustomSecrets []string
}

// GetSecretFieldDefinition returns the exact fields that should be sanitized
func GetSecretFieldDefinition() *SecretFieldDefinition {
	return &SecretFieldDefinition{
		DockerSecrets: []string{
			"dockerPassword",
			"docker_password",
			"dockerCert",
			"docker_cert",
			"dockerCertificate",
			"docker_certificate",
		},
		
		StorageSecrets: []string{
			// AWS/S3 secrets
			"accessKey",
			"access_key",
			"secretKey", 
			"secret_key",
			"passkey",
			"pass_key",
			
			// Azure blob storage
			"accountKey",
			"account_key",
			
			// GCP blob storage
			"credentialFileData",
			"credential_file_data",
			"serviceAccountKey",
			"service_account_key",
		},
		
		DatabaseSecrets: []string{
			"password",
			"dbPassword",
			"db_password",
			"databasePassword",
			"database_password",
			"connectionString",
			"connection_string",
			"databaseUrl",
			"database_url",
			"dbUrl",
			"db_url",
		},
		
		AuthSecrets: []string{
			"token",
			"authToken",
			"auth_token",
			"accessToken",
			"access_token",
			"refreshToken",
			"refresh_token",
			"apiKey",
			"api_key",
			"clientSecret",
			"client_secret",
			"bearerToken",
			"bearer_token",
		},
		
		CertificateSecrets: []string{
			"privateKey",
			"private_key",
			"publicKey",
			"public_key",
			"cert",
			"certificate",
			"tlsCert",
			"tls_cert",
			"sslCert",
			"ssl_cert",
			"caCert",
			"ca_cert",
			"clientCert",
			"client_cert",
			"serverCert",
			"server_cert",
		},
		
		CustomSecrets: []string{
			// Add any additional custom secret fields here
		},
	}
}

// NestedSecretFieldDefinition defines nested field paths that should be sanitized
type NestedSecretFieldDefinition struct {
	// Blob storage configuration paths
	BlobStoragePaths []string
	
	// Docker registry configuration paths
	DockerRegistryPaths []string
	
	// Database configuration paths
	DatabasePaths []string
	
	// External service configuration paths
	ExternalServicePaths []string
	
	// Cloud provider configuration paths
	CloudProviderPaths []string
}

// GetNestedSecretFieldDefinition returns nested field paths that should be sanitized
func GetNestedSecretFieldDefinition() *NestedSecretFieldDefinition {
	return &NestedSecretFieldDefinition{
		BlobStoragePaths: []string{
			"blobStorageS3Config.accessKey",
			"blobStorageS3Config.secretKey",
			"blobStorageS3Config.passkey",
			"blobStorageS3Config.password",
			"azureBlobConfig.accountKey",
			"azureBlobConfig.password",
			"gcpBlobConfig.credentialFileData",
			"gcpBlobConfig.serviceAccountKey",
			"gcpBlobConfig.privateKey",
		},
		
		DockerRegistryPaths: []string{
			"dockerRegistry.password",
			"dockerRegistry.dockerPassword",
			"dockerRegistry.accessKey",
			"dockerRegistry.secretKey",
			"dockerRegistry.cert",
			"dockerRegistry.certificate",
			"dockerRegistryConfig.password",
			"dockerRegistryConfig.dockerPassword",
		},
		
		DatabasePaths: []string{
			"database.password",
			"database.connectionString",
			"database.url",
			"dbConfig.password",
			"dbConfig.connectionString",
			"databaseConfig.password",
			"databaseConfig.url",
		},
		
		ExternalServicePaths: []string{
			"externalService.apiKey",
			"externalService.token",
			"externalService.secret",
			"externalService.password",
			"gitCredentials.password",
			"gitCredentials.token",
			"gitCredentials.privateKey",
		},
		
		CloudProviderPaths: []string{
			"awsConfig.secretAccessKey",
			"awsConfig.accessKeyId",
			"gcpConfig.serviceAccountKey",
			"gcpConfig.credentialFileData",
			"azureConfig.clientSecret",
			"azureConfig.password",
			"cloudConfig.accessKey",
			"cloudConfig.secretKey",
		},
	}
}

// RuntimeParameterSecretConfig defines how to detect secrets in runtime parameters
type RuntimeParameterSecretConfig struct {
	// Enable runtime parameter secret detection
	EnableDetection bool
	
	// Minimum length for secret detection
	MinLength int
	
	// Secret indicators in runtime parameter values
	SecretIndicators []string
	
	// Field name patterns that indicate secrets in runtime parameters
	SecretFieldPatterns []string
}

// GetRuntimeParameterSecretConfig returns configuration for runtime parameter secret detection
func GetRuntimeParameterSecretConfig() *RuntimeParameterSecretConfig {
	return &RuntimeParameterSecretConfig{
		EnableDetection: true,
		MinLength:       12,
		
		SecretIndicators: []string{
			// API key prefixes
			"sk-", "pk-", "rk-",
			
			// GitHub tokens
			"ghp_", "gho_", "ghu_", "ghs_",
			
			// Slack tokens
			"xoxb-", "xoxp-", "xapp-",
			
			// AWS keys
			"AKIA", "ASIA",
			
			// Auth headers
			"Bearer ", "Basic ",
			
			// Certificates
			"-----BEGIN", "-----END",
		},
		
		SecretFieldPatterns: []string{
			// Runtime parameter names that likely contain secrets
			"*password*",
			"*secret*",
			"*token*",
			"*key*",
			"*credential*",
			"*auth*",
			"*cert*",
			"*private*",
		},
	}
}

/*
Usage Examples:

1. **Basic Configuration** (Only known fields):
   AUDIT_ENABLE_FIELD_SPECIFIC_SANITIZATION=true
   AUDIT_ENABLE_RUNTIME_PARAMETER_DETECTION=false

2. **Enhanced Configuration** (Known fields + runtime detection):
   AUDIT_ENABLE_FIELD_SPECIFIC_SANITIZATION=true
   AUDIT_ENABLE_RUNTIME_PARAMETER_DETECTION=true
   AUDIT_RUNTIME_PARAMETER_MIN_LENGTH=12

3. **Debug Configuration** (With logging):
   AUDIT_ENABLE_FIELD_SPECIFIC_SANITIZATION=true
   AUDIT_LOG_SANITIZATION_ACTIONS=true

4. **Custom Fields** (Additional secret fields):
   AUDIT_CUSTOM_SECRET_FIELDS="myCustomSecret,anotherSecretField,thirdSecret"

Benefits of Field-Specific Approach:

1. **Precise Control**: Only sanitize exactly what you specify
2. **No False Positives**: Won't sanitize non-secret data
3. **Performance**: Faster than pattern matching
4. **Maintainable**: Easy to add/remove fields
5. **Predictable**: Consistent behavior across environments

Field Categories Covered:

✅ dockerPassword
✅ accessKey, secretKey  
✅ dockerCert
✅ blobStorageS3Config.accessKey
✅ blobStorageS3Config.passkey
✅ azureBlobConfig.accountKey
✅ gcpBlobConfig.credentialFileData
✅ Runtime parameters (with heuristic detection)

This approach ensures that:
- Only specified fields are sanitized
- Runtime parameters are handled with smart detection
- No secrets leak into audit logs
- Retrigger works with empty values (secure)
- Easy to extend with new secret fields
*/
