/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package repository

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"time"
)

type WorkflowTriggerAuditRepository interface {
	Save(audit *WorkflowTriggerAudit) (*WorkflowTriggerAudit, error)
	SaveWithTx(tx *pg.Tx, audit *WorkflowTriggerAudit) (*WorkflowTriggerAudit, error)
	Update(audit *WorkflowTriggerAudit) error
	UpdateWithTx(tx *pg.Tx, audit *WorkflowTriggerAudit) error
	FindById(id int) (*WorkflowTriggerAudit, error)
	FindByWorkflowIdAndType(workflowId int, workflowType WorkflowType) (*WorkflowTriggerAudit, error)
	FindByPipelineId(pipelineId int, limit int, offset int) ([]*WorkflowTriggerAudit, error)
	FindByAppId(appId int, limit int, offset int) ([]*WorkflowTriggerAudit, error)
	FindLatestByPipelineIdAndType(pipelineId int, workflowType WorkflowType) (*WorkflowTriggerAudit, error)
	sql.TransactionWrapper
}

type WorkflowTriggerAuditRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
	*sql.TransactionUtilImpl
}

func NewWorkflowTriggerAuditRepositoryImpl(dbConnection *pg.DB, logger *zap.SugaredLogger, transactionUtilImpl *sql.TransactionUtilImpl) *WorkflowTriggerAuditRepositoryImpl {
	return &WorkflowTriggerAuditRepositoryImpl{
		dbConnection:         dbConnection,
		logger:               logger,
		TransactionUtilImpl:  transactionUtilImpl,
	}
}

type WorkflowType string

const (
	CI_WORKFLOW_TYPE      WorkflowType = "CI"
	PRE_CD_WORKFLOW_TYPE  WorkflowType = "PRE_CD"
	POST_CD_WORKFLOW_TYPE WorkflowType = "POST_CD"
	DEPLOY_CD_WORKFLOW_TYPE WorkflowType = "DEPLOY_CD"
)

type TriggerType string

const (
	MANUAL_TRIGGER  TriggerType = "MANUAL"
	AUTO_TRIGGER    TriggerType = "AUTO"
	WEBHOOK_TRIGGER TriggerType = "WEBHOOK"
)

type AuditStatus string

const (
	AUDIT_SAVED AuditStatus = "SAVED"
)

type WorkflowTriggerAudit struct {
	tableName                struct{}    `sql:"workflow_trigger_audit" pg:",discard_unknown_columns"`
	Id                       int         `sql:"id,pk"`
	WorkflowId               int         `sql:"workflow_id,notnull"`
	WorkflowType             WorkflowType `sql:"workflow_type,notnull"`
	PipelineId               int         `sql:"pipeline_id,notnull"`
	AppId                    int         `sql:"app_id,notnull"`
	EnvironmentId            *int        `sql:"environment_id"`
	ArtifactId               *int        `sql:"artifact_id"`
	TriggerType              TriggerType `sql:"trigger_type,notnull"`
	TriggeredBy              int32       `sql:"triggered_by,notnull"`
	TriggerMetadata          string      `sql:"trigger_metadata"`
	WorkflowConfigSnapshotId int         `sql:"workflow_config_snapshot_id,notnull"`
	InfraConfigSnapshotId    *int        `sql:"infra_config_snapshot_id"`
	Status                   AuditStatus `sql:"status,notnull"`
	sql.AuditLog
}

func (impl *WorkflowTriggerAuditRepositoryImpl) Save(audit *WorkflowTriggerAudit) (*WorkflowTriggerAudit, error) {
	err := impl.dbConnection.Insert(audit)
	if err != nil {
		impl.logger.Errorw("error in saving workflow trigger audit", "err", err, "audit", audit)
		return audit, err
	}
	return audit, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) SaveWithTx(tx *pg.Tx, audit *WorkflowTriggerAudit) (*WorkflowTriggerAudit, error) {
	err := tx.Insert(audit)
	if err != nil {
		impl.logger.Errorw("error in saving workflow trigger audit with tx", "err", err, "audit", audit)
		return audit, err
	}
	return audit, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) Update(audit *WorkflowTriggerAudit) error {
	err := impl.dbConnection.Update(audit)
	if err != nil {
		impl.logger.Errorw("error in updating workflow trigger audit", "err", err, "audit", audit)
		return err
	}
	return nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) UpdateWithTx(tx *pg.Tx, audit *WorkflowTriggerAudit) error {
	err := tx.Update(audit)
	if err != nil {
		impl.logger.Errorw("error in updating workflow trigger audit with tx", "err", err, "audit", audit)
		return err
	}
	return nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) FindById(id int) (*WorkflowTriggerAudit, error) {
	audit := &WorkflowTriggerAudit{}
	err := impl.dbConnection.Model(audit).
		Where("id = ?", id).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow trigger audit by id", "err", err, "id", id)
		return audit, err
	}
	return audit, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) FindByWorkflowIdAndType(workflowId int, workflowType WorkflowType) (*WorkflowTriggerAudit, error) {
	audit := &WorkflowTriggerAudit{}
	err := impl.dbConnection.Model(audit).
		Where("workflow_id = ?", workflowId).
		Where("workflow_type = ?", workflowType).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow trigger audit by workflow id and type", "err", err, "workflowId", workflowId, "workflowType", workflowType)
		return audit, err
	}
	return audit, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) FindByPipelineId(pipelineId int, limit int, offset int) ([]*WorkflowTriggerAudit, error) {
	var audits []*WorkflowTriggerAudit
	err := impl.dbConnection.Model(&audits).
		Where("pipeline_id = ?", pipelineId).
		Order("created_on DESC").
		Limit(limit).
		Offset(offset).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow trigger audits by pipeline id", "err", err, "pipelineId", pipelineId)
		return audits, err
	}
	return audits, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) FindByAppId(appId int, limit int, offset int) ([]*WorkflowTriggerAudit, error) {
	var audits []*WorkflowTriggerAudit
	err := impl.dbConnection.Model(&audits).
		Where("app_id = ?", appId).
		Order("created_on DESC").
		Limit(limit).
		Offset(offset).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow trigger audits by app id", "err", err, "appId", appId)
		return audits, err
	}
	return audits, nil
}

func (impl *WorkflowTriggerAuditRepositoryImpl) FindLatestByPipelineIdAndType(pipelineId int, workflowType WorkflowType) (*WorkflowTriggerAudit, error) {
	audit := &WorkflowTriggerAudit{}
	err := impl.dbConnection.Model(audit).
		Where("pipeline_id = ?", pipelineId).
		Where("workflow_type = ?", workflowType).
		Order("created_on DESC").
		Limit(1).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding latest workflow trigger audit by pipeline id and type", "err", err, "pipelineId", pipelineId, "workflowType", workflowType)
		return audit, err
	}
	return audit, nil
}

// WorkflowConfigSnapshot repository
type WorkflowConfigSnapshotRepository interface {
	Save(snapshot *WorkflowConfigSnapshot) (*WorkflowConfigSnapshot, error)
	SaveWithTx(tx *pg.Tx, snapshot *WorkflowConfigSnapshot) (*WorkflowConfigSnapshot, error)
	FindById(id int) (*WorkflowConfigSnapshot, error)
	FindByPipelineIdAndType(pipelineId int, workflowType WorkflowType, limit int, offset int) ([]*WorkflowConfigSnapshot, error)
	sql.TransactionWrapper
}

type WorkflowConfigSnapshotRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
	*sql.TransactionUtilImpl
}

func NewWorkflowConfigSnapshotRepositoryImpl(dbConnection *pg.DB, logger *zap.SugaredLogger, transactionUtilImpl *sql.TransactionUtilImpl) *WorkflowConfigSnapshotRepositoryImpl {
	return &WorkflowConfigSnapshotRepositoryImpl{
		dbConnection:         dbConnection,
		logger:               logger,
		TransactionUtilImpl:  transactionUtilImpl,
	}
}

type WorkflowConfigSnapshot struct {
	tableName              struct{}    `sql:"workflow_config_snapshot" pg:",discard_unknown_columns"`
	Id                     int         `sql:"id,pk"`
	WorkflowType           WorkflowType `sql:"workflow_type,notnull"`
	PipelineId             int         `sql:"pipeline_id,notnull"`
	AppId                  int         `sql:"app_id,notnull"`
	EnvironmentId          *int        `sql:"environment_id"`
	WorkflowRequestJson    string      `sql:"workflow_request_json,notnull"`
	PipelineConfigJson     string      `sql:"pipeline_config_json,notnull"`
	DeploymentConfigJson   *string     `sql:"deployment_config_json"`
	GitMaterialsJson       *string     `sql:"git_materials_json"`
	CiPipelineScriptsJson  *string     `sql:"ci_pipeline_scripts_json"`
	PrePostScriptsJson     *string     `sql:"pre_post_scripts_json"`
	ConfigMapsJson         *string     `sql:"config_maps_json"`
	SecretsJson            *string     `sql:"secrets_json"`
	StrategyConfigJson     *string     `sql:"strategy_config_json"`
	sql.AuditLog
}

func (impl *WorkflowConfigSnapshotRepositoryImpl) Save(snapshot *WorkflowConfigSnapshot) (*WorkflowConfigSnapshot, error) {
	err := impl.dbConnection.Insert(snapshot)
	if err != nil {
		impl.logger.Errorw("error in saving workflow config snapshot", "err", err, "snapshot", snapshot)
		return snapshot, err
	}
	return snapshot, nil
}

func (impl *WorkflowConfigSnapshotRepositoryImpl) SaveWithTx(tx *pg.Tx, snapshot *WorkflowConfigSnapshot) (*WorkflowConfigSnapshot, error) {
	err := tx.Insert(snapshot)
	if err != nil {
		impl.logger.Errorw("error in saving workflow config snapshot with tx", "err", err, "snapshot", snapshot)
		return snapshot, err
	}
	return snapshot, nil
}

func (impl *WorkflowConfigSnapshotRepositoryImpl) FindById(id int) (*WorkflowConfigSnapshot, error) {
	snapshot := &WorkflowConfigSnapshot{}
	err := impl.dbConnection.Model(snapshot).
		Where("id = ?", id).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow config snapshot by id", "err", err, "id", id)
		return snapshot, err
	}
	return snapshot, nil
}

func (impl *WorkflowConfigSnapshotRepositoryImpl) FindByPipelineIdAndType(pipelineId int, workflowType WorkflowType, limit int, offset int) ([]*WorkflowConfigSnapshot, error) {
	var snapshots []*WorkflowConfigSnapshot
	err := impl.dbConnection.Model(&snapshots).
		Where("pipeline_id = ?", pipelineId).
		Where("workflow_type = ?", workflowType).
		Order("created_on DESC").
		Limit(limit).
		Offset(offset).
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding workflow config snapshots by pipeline id and type", "err", err, "pipelineId", pipelineId, "workflowType", workflowType)
		return snapshots, err
	}
	return snapshots, nil
}

// TriggerMaterialSnapshot repository
type TriggerMaterialSnapshotRepository interface {
	Save(snapshot *TriggerMaterialSnapshot) (*TriggerMaterialSnapshot, error)
	SaveWithTx(tx *pg.Tx, snapshot *TriggerMaterialSnapshot) (*TriggerMaterialSnapshot, error)
	SaveBatch(snapshots []*TriggerMaterialSnapshot) error
	SaveBatchWithTx(tx *pg.Tx, snapshots []*TriggerMaterialSnapshot) error
	FindByWorkflowTriggerAuditId(auditId int) ([]*TriggerMaterialSnapshot, error)
	sql.TransactionWrapper
}

type TriggerMaterialSnapshotRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
	*sql.TransactionUtilImpl
}

func NewTriggerMaterialSnapshotRepositoryImpl(dbConnection *pg.DB, logger *zap.SugaredLogger, transactionUtilImpl *sql.TransactionUtilImpl) *TriggerMaterialSnapshotRepositoryImpl {
	return &TriggerMaterialSnapshotRepositoryImpl{
		dbConnection:         dbConnection,
		logger:               logger,
		TransactionUtilImpl:  transactionUtilImpl,
	}
}

type TriggerMaterialSnapshot struct {
	tableName               struct{}  `sql:"trigger_material_snapshot" pg:",discard_unknown_columns"`
	Id                      int       `sql:"id,pk"`
	WorkflowTriggerAuditId  int       `sql:"workflow_trigger_audit_id,notnull"`
	GitMaterialId           *int      `sql:"git_material_id"`
	CommitHash              *string   `sql:"commit_hash"`
	CommitMessage           *string   `sql:"commit_message"`
	CommitAuthor            *string   `sql:"commit_author"`
	CommitDate              *time.Time `sql:"commit_date"`
	Branch                  *string   `sql:"branch"`
	Tag                     *string   `sql:"tag"`
	WebhookData             *string   `sql:"webhook_data"`
	sql.AuditLog
}

func (impl *TriggerMaterialSnapshotRepositoryImpl) Save(snapshot *TriggerMaterialSnapshot) (*TriggerMaterialSnapshot, error) {
	err := impl.dbConnection.Insert(snapshot)
	if err != nil {
		impl.logger.Errorw("error in saving trigger material snapshot", "err", err, "snapshot", snapshot)
		return snapshot, err
	}
	return snapshot, nil
}

func (impl *TriggerMaterialSnapshotRepositoryImpl) SaveWithTx(tx *pg.Tx, snapshot *TriggerMaterialSnapshot) (*TriggerMaterialSnapshot, error) {
	err := tx.Insert(snapshot)
	if err != nil {
		impl.logger.Errorw("error in saving trigger material snapshot with tx", "err", err, "snapshot", snapshot)
		return snapshot, err
	}
	return snapshot, nil
}

func (impl *TriggerMaterialSnapshotRepositoryImpl) SaveBatch(snapshots []*TriggerMaterialSnapshot) error {
	if len(snapshots) == 0 {
		return nil
	}
	err := impl.dbConnection.Insert(&snapshots)
	if err != nil {
		impl.logger.Errorw("error in saving trigger material snapshots batch", "err", err, "count", len(snapshots))
		return err
	}
	return nil
}

func (impl *TriggerMaterialSnapshotRepositoryImpl) SaveBatchWithTx(tx *pg.Tx, snapshots []*TriggerMaterialSnapshot) error {
	if len(snapshots) == 0 {
		return nil
	}
	err := tx.Insert(&snapshots)
	if err != nil {
		impl.logger.Errorw("error in saving trigger material snapshots batch with tx", "err", err, "count", len(snapshots))
		return err
	}
	return nil
}

func (impl *TriggerMaterialSnapshotRepositoryImpl) FindByWorkflowTriggerAuditId(auditId int) ([]*TriggerMaterialSnapshot, error) {
	var snapshots []*TriggerMaterialSnapshot
	err := impl.dbConnection.Model(&snapshots).
		Where("workflow_trigger_audit_id = ?", auditId).
		Order("id ASC").
		Select()
	if err != nil {
		impl.logger.Errorw("error in finding trigger material snapshots by audit id", "err", err, "auditId", auditId)
		return snapshots, err
	}
	return snapshots, nil
}
