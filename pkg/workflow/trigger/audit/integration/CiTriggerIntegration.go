/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package integration

import (
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/bean"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/helper"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/hook"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/repository"
	"go.uber.org/zap"
)

// CiTriggerIntegration provides integration points for CI trigger audit
type CiTriggerIntegration interface {
	// AuditCiTrigger audits CI trigger at trigger time
	AuditCiTrigger(trigger types.Trigger, pipeline *pipelineConfig.CiPipeline, workflowRequest *types.WorkflowRequest, workflowId int, infraConfigSnapshotId *int) error
}

type CiTriggerIntegrationImpl struct {
	logger              *zap.SugaredLogger
	triggerAuditHook    hook.TriggerAuditHook
	gitMaterialHelper   helper.GitMaterialHelper
}

func NewCiTriggerIntegrationImpl(logger *zap.SugaredLogger, triggerAuditHook hook.TriggerAuditHook, gitMaterialHelper helper.GitMaterialHelper) *CiTriggerIntegrationImpl {
	return &CiTriggerIntegrationImpl{
		logger:              logger,
		triggerAuditHook:    triggerAuditHook,
		gitMaterialHelper:   gitMaterialHelper,
	}
}

func (impl *CiTriggerIntegrationImpl) AuditCiTriggerStart(trigger types.Trigger, pipeline *pipelineConfig.CiPipeline, workflowRequest *types.WorkflowRequest, workflowId int, infraConfigSnapshotId *int) error {
	impl.logger.Infow("starting CI trigger audit", "workflowId", workflowId, "pipelineId", trigger.PipelineId)

	// Extract git materials from trigger
	gitMaterials, err := impl.gitMaterialHelper.ExtractGitMaterialsFromCiTrigger(trigger)
	if err != nil {
		impl.logger.Errorw("error in extracting git materials from CI trigger", "err", err, "workflowId", workflowId)
		return err
	}

	// Determine trigger type
	triggerType := helper.DetermineTriggerType(false, false) // This would need to be determined based on actual trigger context

	// Create trigger metadata
	triggerMetadata := helper.CreateTriggerMetadata(map[string]interface{}{
		"pipelineInfo": helper.ExtractCiPipelineInfo(pipeline),
		"triggerContext": map[string]interface{}{
			"runtimeParameters": trigger.RuntimeParameters,
		},
	})

	// Call audit hook
	err = impl.triggerAuditHook.AuditCiTrigger(
		workflowId,
		pipeline,
		workflowRequest,
		gitMaterials,
		triggerType,
		trigger.TriggeredBy,
		triggerMetadata,
		infraConfigSnapshotId,
	)

	if err != nil {
		impl.logger.Errorw("error in auditing CI trigger start", "err", err, "workflowId", workflowId)
		return err
	}

	impl.logger.Infow("successfully audited CI trigger start", "workflowId", workflowId, "pipelineId", trigger.PipelineId)
	return nil
}

func (impl *CiTriggerIntegrationImpl) AuditCiTriggerSuccess(workflowId int) error {
	impl.logger.Infow("auditing CI trigger success", "workflowId", workflowId)

	err := impl.triggerAuditHook.UpdateTriggerAuditStatus(workflowId, repository.CI_WORKFLOW_TYPE, repository.AUDIT_SUCCESS)
	if err != nil {
		impl.logger.Errorw("error in updating CI trigger audit status to success", "err", err, "workflowId", workflowId)
		return err
	}

	impl.logger.Infow("successfully audited CI trigger success", "workflowId", workflowId)
	return nil
}

func (impl *CiTriggerIntegrationImpl) AuditCiTriggerFailure(workflowId int, errorMessage string) error {
	impl.logger.Infow("auditing CI trigger failure", "workflowId", workflowId, "error", errorMessage)

	err := impl.triggerAuditHook.UpdateTriggerAuditStatus(workflowId, repository.CI_WORKFLOW_TYPE, repository.AUDIT_FAILED)
	if err != nil {
		impl.logger.Errorw("error in updating CI trigger audit status to failed", "err", err, "workflowId", workflowId)
		return err
	}

	impl.logger.Infow("successfully audited CI trigger failure", "workflowId", workflowId)
	return nil
}

/*
Example integration in existing CI trigger handler:

// In pkg/build/trigger/HandlerService.go, modify the triggerCiPipeline method:

func (impl *HandlerServiceImpl) triggerCiPipeline(trigger types.Trigger) (int, error) {
	pipeline, variableSnapshot, savedCiWf, workflowRequest, err := impl.StartCiWorkflowAndPrepareWfRequest(trigger)
	if err != nil {
		return 0, err
	}

	// AUDIT: Start CI trigger audit
	err = impl.ciTriggerIntegration.AuditCiTriggerStart(trigger, pipeline, workflowRequest, savedCiWf.Id, nil)
	if err != nil {
		impl.Logger.Errorw("error in auditing CI trigger start", "err", err, "workflowId", savedCiWf.Id)
		// Continue with trigger even if audit fails
	}

	workflowRequest.CiPipelineType = trigger.PipelineType
	err = impl.executeCiPipeline(workflowRequest)
	if err != nil {
		impl.Logger.Errorw("error in executing ci pipeline", "err", err)

		// AUDIT: CI trigger failure
		auditErr := impl.ciTriggerIntegration.AuditCiTriggerFailure(savedCiWf.Id, err.Error())
		if auditErr != nil {
			impl.Logger.Errorw("error in auditing CI trigger failure", "err", auditErr, "workflowId", savedCiWf.Id)
		}

		dbErr := impl.markCurrentCiWorkflowFailed(savedCiWf, err)
		if dbErr != nil {
			impl.Logger.Errorw("update ci workflow error", "err", dbErr)
		}
		return 0, err
	}

	// AUDIT: CI trigger success (this would typically be called from a workflow completion handler)
	// auditErr := impl.ciTriggerIntegration.AuditCiTriggerSuccess(savedCiWf.Id)
	// if auditErr != nil {
	//     impl.Logger.Errorw("error in auditing CI trigger success", "err", auditErr, "workflowId", savedCiWf.Id)
	// }

	impl.Logger.Debugw("ci triggered", " pipeline ", trigger.PipelineId)

	// ... rest of the existing code

	return savedCiWf.Id, err
}

// For workflow completion, you would add audit calls in the workflow status update handlers
*/

// RetriggerIntegration provides integration for retrigger functionality
type RetriggerIntegration interface {
	// RetriggerFromAudit retriggers workflow from audit snapshot
	RetriggerFromAudit(auditId int, triggeredBy int32) (int, error)
}

type RetriggerIntegrationImpl struct {
	logger              *zap.SugaredLogger
	triggerAuditHook    hook.TriggerAuditHook
	// Add references to CI and CD trigger services here
}

func NewRetriggerIntegrationImpl(logger *zap.SugaredLogger, triggerAuditHook hook.TriggerAuditHook) *RetriggerIntegrationImpl {
	return &RetriggerIntegrationImpl{
		logger:              logger,
		triggerAuditHook:    triggerAuditHook,
	}
}

func (impl *RetriggerIntegrationImpl) RetriggerFromAudit(auditId int, triggeredBy int32) (int, error) {
	impl.logger.Infow("starting retrigger from audit", "auditId", auditId, "triggeredBy", triggeredBy)

	// Get retrigger config from audit
	config, err := impl.triggerAuditHook.GetRetriggerConfig(auditId)
	if err != nil {
		impl.logger.Errorw("error in getting retrigger config", "err", err, "auditId", auditId)
		return 0, err
	}

	impl.logger.Infow("retrieved retrigger config", "auditId", auditId, "workflowType", config.WorkflowType, "pipelineId", config.PipelineId)

	// Based on workflow type, call appropriate trigger service
	switch config.WorkflowType {
	case "CI":
		return impl.retriggerCi(config, triggeredBy)
	case "PRE_CD":
		return impl.retriggerPreCd(config, triggeredBy)
	case "POST_CD":
		return impl.retriggerPostCd(config, triggeredBy)
	case "DEPLOY_CD":
		return impl.retriggerDeployCd(config, triggeredBy)
	default:
		return 0, fmt.Errorf("unsupported workflow type for retrigger: %s", config.WorkflowType)
	}
}

func (impl *RetriggerIntegrationImpl) retriggerCi(config *bean.RetriggerWorkflowConfig, triggeredBy int32) (int, error) {
	impl.logger.Infow("retriggering CI workflow", "auditId", config.AuditId, "pipelineId", config.PipelineId)

	// This would need to be implemented to call the CI trigger service with the historical config
	// The workflow request from the snapshot would be used instead of building a new one

	// Pseudo-code:
	// 1. Update workflow request with new triggered by
	// 2. Call CI trigger service with historical workflow request
	// 3. Return new workflow ID

	return 0, fmt.Errorf("CI retrigger not implemented yet")
}

func (impl *RetriggerIntegrationImpl) retriggerPreCd(config *bean.RetriggerWorkflowConfig, triggeredBy int32) (int, error) {
	impl.logger.Infow("retriggering Pre-CD workflow", "auditId", config.AuditId, "pipelineId", config.PipelineId)

	// Similar implementation for Pre-CD retrigger
	return 0, fmt.Errorf("Pre-CD retrigger not implemented yet")
}

func (impl *RetriggerIntegrationImpl) retriggerPostCd(config *bean.RetriggerWorkflowConfig, triggeredBy int32) (int, error) {
	impl.logger.Infow("retriggering Post-CD workflow", "auditId", config.AuditId, "pipelineId", config.PipelineId)

	// Similar implementation for Post-CD retrigger
	return 0, fmt.Errorf("Post-CD retrigger not implemented yet")
}

func (impl *RetriggerIntegrationImpl) retriggerDeployCd(config *bean.RetriggerWorkflowConfig, triggeredBy int32) (int, error) {
	impl.logger.Infow("retriggering Deploy-CD workflow", "auditId", config.AuditId, "pipelineId", config.PipelineId)

	// Similar implementation for Deploy-CD retrigger
	return 0, fmt.Errorf("Deploy-CD retrigger not implemented yet")
}
